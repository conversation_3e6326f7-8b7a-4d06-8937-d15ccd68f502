import Foundation
import SwiftUI

// Test file to verify LaTeX number processing behavior
struct LaTeXNumberTest {
    
    static func runTests() {
        print("🧪 Running LaTeX Number Processing Tests")
        
        // Test cases that should keep $8$ and $12$ as LaTeX in mathematical contexts
        let testCases = [
            "What is the area of a trapezium with parallel sides measuring $8$ cm and $12$ cm, and a perpendicular height of $5$ cm?",
            "The rectangle has dimensions $8$ by $12$ meters.",
            "Calculate the perimeter when length is $8$ and width is $12$.",
            "Find the area of a triangle with base $8$ cm and height $12$ cm.",
        ]

        // Test cases that should convert $8$ and $12$ to plain text (non-mathematical contexts)
        let currencyTestCases = [
            "The item costs $8$ and shipping is $12$.",
            "Simple numbers: $8$ and $12$ should be plain text.",
        ]
        
        // Test mathematical contexts (should keep LaTeX)
        for (index, testCase) in testCases.enumerated() {
            print("\n--- Mathematical Test Case \(index + 1) ---")
            print("Input: \(testCase)")

            // Test LaTeX detection on original text
            let hasLaTeX = containsLaTeX(testCase)
            print("Contains LaTeX: \(hasLaTeX)")

            // Test the preprocessing function
            let processed = preprocessTextForRendering(testCase)
            print("Processed: \(processed)")

            // Check if $8$ and $12$ are still in LaTeX format (should be for math contexts)
            let contains8 = processed.contains("$8$")
            let contains12 = processed.contains("$12$")
            print("Still contains $8$: \(contains8)")
            print("Still contains $12$: \(contains12)")

            if contains8 || contains12 {
                print("✅ SUCCESS: Numbers preserved as LaTeX in mathematical context")
            } else {
                print("❌ FAILED: Numbers incorrectly converted to plain text in mathematical context")
            }
        }

        // Test non-mathematical contexts (should convert to plain text)
        for (index, testCase) in currencyTestCases.enumerated() {
            print("\n--- Currency Test Case \(index + 1) ---")
            print("Input: \(testCase)")

            // Test LaTeX detection on original text
            let hasLaTeX = containsLaTeX(testCase)
            print("Contains LaTeX: \(hasLaTeX)")

            // Test the preprocessing function
            let processed = preprocessTextForRendering(testCase)
            print("Processed: \(processed)")

            // Check if $8$ and $12$ were converted to plain text
            let contains8 = processed.contains("$8$")
            let contains12 = processed.contains("$12$")
            print("Still contains $8$: \(contains8)")
            print("Still contains $12$: \(contains12)")

            if !contains8 && !contains12 {
                print("✅ SUCCESS: Numbers converted to plain text in non-mathematical context")
            } else {
                print("❌ FAILED: Numbers still in LaTeX format in non-mathematical context")
            }
        }
        
        print("\n🧪 LaTeX Number Processing Tests Complete")
    }
}

// Helper functions (copied from LaTeXTextRenderer for testing)
private func preprocessTextForRendering(_ text: String) -> String {
    print("🔧 LaTeX Preprocessing: Input: '\(text)'")

    // Step 1: Check for LaTeX BEFORE processing to preserve original LaTeX commands
    let hasLaTeX = containsLaTeX(text)
    print("🔧 LaTeX Preprocessing: Original text contains LaTeX: \(hasLaTeX)")

    // Step 2: Handle all currency and math patterns comprehensively
    var processedText = handleAllMathAndCurrencyPatterns(text)
    print("🔧 LaTeX Preprocessing: After math/currency: '\(processedText)'")

    // Step 3: Handle newlines only if not in math context
    if !hasLaTeX {
        processedText = handleNewlines(processedText)
        print("🔧 LaTeX Preprocessing: After newlines: '\(processedText)'")
    } else {
        print("🔧 LaTeX Preprocessing: Skipping newline processing for LaTeX content")
    }

    print("🔧 LaTeX Preprocessing: Final: '\(processedText)'")
    return processedText
}

private func containsLaTeX(_ text: String) -> Bool {
    var textToCheck = text

    // Check if this is a mathematical context before removing simple numbers
    let mathKeywords = ["area", "trapezium", "parallel", "perpendicular", "height", "measuring", "cm", "angle", "triangle", "circle", "radius", "diameter", "perimeter", "volume", "surface", "degrees", "°"]
    let containsMathKeywords = mathKeywords.contains { text.lowercased().contains($0) }

    // Only remove simple number patterns from LaTeX detection if NOT in a mathematical context
    if !containsMathKeywords && !text.contains("°") && !text.contains("cm") && !text.contains("mm") {
        // Remove simple numbers like $8$, $12$, $5$, $360$, $90$, etc. but NOT variables like $x$, $y$
        let simpleNumberPattern = #"\$\d+\$"#
        textToCheck = textToCheck.replacingOccurrences(
            of: simpleNumberPattern,
            with: "",
            options: .regularExpression
        )
    }

    let hasInlineMath = textToCheck.range(of: #"\$[^$]*\\[a-zA-Z]+[^$]*\$"#, options: .regularExpression) != nil
    let hasDisplayMath = textToCheck.range(of: #"\$\$[^$]+\$\$"#, options: .regularExpression) != nil

    // Check for mathematical expressions that should be rendered as LaTeX
    let hasMathExpressions =
        textToCheck.range(of: #"\$[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
        textToCheck.range(of: #"\$\d+[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
        textToCheck.range(of: #"\$[^$]*[a-zA-Z]+[^$]*[+\-*/][^$]*\$"#, options: .regularExpression) != nil ||
        // Simple numbers in mathematical contexts (like geometry problems)
        (containsMathKeywords && textToCheck.range(of: #"\$\d+\$"#, options: .regularExpression) != nil)

    return hasInlineMath || hasDisplayMath || hasMathExpressions
}

private func handleAllMathAndCurrencyPatterns(_ text: String) -> String {
    var result = text

    // Handle pure numeric dollar amounts ONLY in clear currency contexts
    let pureNumberPattern = #"\$(-?\d+(?:\.\d+)?)\$"#

    // Only convert to plain numbers if this is clearly a currency context, not a mathematical context
    let mathKeywords = ["area", "trapezium", "parallel", "perpendicular", "height", "measuring", "cm", "angle", "triangle", "circle", "radius", "diameter", "perimeter", "volume", "surface", "degrees", "°"]
    let containsMathKeywords = mathKeywords.contains { result.lowercased().contains($0) }

    let shouldSkipConversion = result.contains("(") ||
                              result.contains(",") ||
                              result.contains(")") ||
                              containsMathKeywords ||
                              result.contains("°") ||
                              result.contains("cm") ||
                              result.contains("mm") ||
                              result.contains("m²") ||
                              result.contains("²") ||
                              result.contains("³")

    if !shouldSkipConversion {
        result = result.replacingOccurrences(
            of: pureNumberPattern,
            with: "$1",
            options: .regularExpression
        )
    }

    return result
}

private func handleNewlines(_ text: String) -> String {
    return text.replacingOccurrences(of: "\\n", with: "\n")
}
