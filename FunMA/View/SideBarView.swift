//
//  SideBarView.swift
//  Luminous Education
//
//  Created by <PERSON> on 30/7/2024.
//

import SwiftUI

struct SideBarView: View {
    // @State private var selectedCategoryId: UUID?
    @ObservedObject private var userManager = UserManager.shared
    @ObservedObject private var imageCache = ImageCacheManager.shared

    // Add state for tracking disclosure group expansion
    @State private var isShapesExpanded: Bool = false

    // Cache menu items to avoid recomputation
    @State private var cachedMenuItems: [Item] = []
    @State private var lastUserRole: String = ""

    @Binding var selectedItem: String
    @Binding var showingDetailView: Bool
    @State private var showingUserProfile = false
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    
    struct Item: Identifiable {
        var id = UUID() // Unique identifier
        var name: String
        var image: String // Name of the image asset
        var feature: UserManager.Feature?
    }
    
    // Menu items for students
    let studentMenuItems: [Item] = [
        Item(name: "Dashboard", image: "house"),
        Item(name: "Exercise", image: "pencil"),
        Item(name: "Join Game", image: "gamecontroller.fill"),
        Item(name: "AI Study Assistant", image: "bubble.left.and.bubble.right.fill"),
//        Item(name: "My Courses", image: "book.fill", feature: .myCourses),
//        Item(name: "My Performance", image: "chart.bar.fill", feature: .myPerformance),
//        Item(name: "Recommendations", image: "star.fill", feature: .recommendations),
        // Item(name: "My Classroom", image: "building.2"),
        // Item(name: "Homework", image: "pencil"),
        // Item(name: "Notes", image: "note.text"),
//        Item(name: "Schedule", image: "calendar"),
//        Item(name: "Interactive Tools", image: "slider.horizontal.3"),
//        Item(name: "Marketplace", image: "cart.fill")
    ]
    
    // Menu items for teachers
    let teacherMenuItems: [Item] = [
        Item(name: "Dashboard", image: "house"),
        Item(name: "My Classrooms", image: "building.2.fill"),
        Item(name: "Exercise", image: "pencil"),
        Item(name: "Launch Game", image: "gamecontroller.fill"),
        // Item(name: "Flipped Classroom Resource", image: "video.and.waveform.fill"),
//        Item(name: "Students", image: "person.3.fill"),
//        Item(name: "Courses", image: "book.fill"),
    ]
    
    // Menu items for developers (combines teacher and student features)
    let developerMenuItems: [Item] = [
        Item(name: "Dashboard", image: "house"),
        // Item(name: "My Classrooms", image: "building.2.fill"),
        // Item(name: "My Classroom", image: "building.2"),
        Item(name: "Exercise", image: "checkmark.circle.fill"),
        Item(name: "Launch Game", image: "gamecontroller.fill"),
        Item(name: "Join Game", image: "gamecontroller.fill"),
        // Item(name: "Homework", image: "pencil"),
        Item(name: "Interactive Tools", image: "slider.horizontal.3"),
        Item(name: "Shapes", image: "square.on.circle"),
        Item(name: "AR Cube", image: "cube.fill"), // <-- Added menu item for ARCubeView
        Item(name: "AI Study Assistant", image: "bubble.left.and.bubble.right.fill"),
        Item(name: "School Management", image: "building.2.fill"),
        Item(name: "Batch Account Creation", image: "person.3.fill"),
        Item(name: "Credit Adjustment", image: "creditcard.and.123"),
//        Item(name: "Students", image: "person.3.fill"),
//        Item(name: "Courses", image: "book.fill"),
//        Item(name: "My Courses", image: "book.fill", feature: .myCourses),
//        Item(name: "My Performance", image: "chart.bar.fill", feature: .myPerformance),
//        Item(name: "Recommendations", image: "star.fill", feature: .recommendations),
//        Item(name: "In-Class Exercise", image: "checkmark.circle.fill"),
//        Item(name: "Notes", image: "note.text"),
//        Item(name: "Schedule", image: "calendar"),
//        Item(name: "Marketplace", image: "cart.fill")
    ]
    
    // Menu items for guests
    let guestMenuItems: [Item] = [
        Item(name: "Join Game", image: "gamecontroller.fill"),
    ]

    var body: some View {
        VStack(alignment: .leading) {
            List {
                // Show menu items based on user role
                ForEach(cachedMenuItems) { item in
                    if let feature = item.feature {
                        if userManager.isFeatureAvailable(feature) {
                            Button(action: {
                                selectedItem = item.name
                                if horizontalSizeClass == .compact {
                                    showingDetailView = true
                                }
                            }) {
                                sidebarItemContent(for: item)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    } else {
                        // If no feature is required, show the item
                        Button(action: {
                            selectedItem = item.name
                            if horizontalSizeClass == .compact {
                                showingDetailView = true
                            }
                        }) {
                            sidebarItemContent(for: item)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }

                }
            }
            // .listStyle(PlainListStyle())
            
            // User profile section
            profileSection
        }
        .background(Color.gray.opacity(0.1))
        .sheet(isPresented: $showingUserProfile) {
            UserProfileView()
        }
        .onAppear {
            // Use Task to avoid blocking main thread during initial setup
            Task { @MainActor in
                // Update cached menu items if user role changed
                updateCachedMenuItems()

                // Only set default selection if no item is currently selected
                if selectedItem.isEmpty {
                    if !userManager.currentUser.isGuest {
                        selectedItem = "Dashboard"
                    } else {
                        // For guests, select the first available item (Join Game)
                        selectedItem = guestMenuItems.first?.name ?? "Join Game"
                    }
                }
            }
        }
        .onChange(of: userManager.currentUser.role) { _ in
            // Update cached menu items when user role changes
            updateCachedMenuItems()
        }
    }

    // Helper function to create sidebar item content
    @ViewBuilder
    private func sidebarItemContent(for item: Item) -> some View {
        HStack {
            // Small indicator for selected item
            Rectangle()
                .fill(selectedItem == item.name ? Color.accentColor : Color.clear)
                .frame(width: 3, height: 30)
                .cornerRadius(1.5)

            Image(systemName: item.image)
                .resizable()
                .scaledToFit()
                .frame(width: 30, height: 30)
                .foregroundColor(selectedItem == item.name ? .accentColor : .primary)

            Text(item.name)
                .font(.system(.title3, design: .rounded))
                .bold()
                .foregroundColor(selectedItem == item.name ? .accentColor : .primary)

            Spacer()
        }
        .padding(5)
        .contentShape(Rectangle())
    }

    private var menuItems: [Item] {
        if userManager.currentUser.isDeveloper {
            return developerMenuItems
        } else if userManager.currentUser.isTeacher {
            return teacherMenuItems
        } else if userManager.currentUser.isGuest {
            return guestMenuItems
        } else {
            return studentMenuItems
        }
    }

    // Helper method to update cached menu items
    private func updateCachedMenuItems() {
        let currentRole = userManager.currentUser.role
        if lastUserRole != currentRole {
            cachedMenuItems = menuItems
            lastUserRole = currentRole
        }
    }
    
    
    var profileSection: some View {
        HStack {
            if !userManager.currentUser.isGuest {
                Button(action: {
                    showingUserProfile = true
                }) {
                    HStack {
                        // Profile image or default icon
                        if let profileImageUrl = userManager.currentUser.profileImageUrl,
                           !profileImageUrl.isEmpty {
                            CachedAsyncImage(url: profileImageUrl) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 50, height: 50)
                                    .clipShape(Circle())
                            } placeholder: {
                                ZStack {
                                    Circle()
                                        .fill(Color.gray.opacity(0.3))
                                        .frame(width: 50, height: 50)

                                    if imageCache.isLoading(profileImageUrl) {
                                        ProgressView()
                                            .scaleEffect(0.6)
                                            .tint(.white)
                                    } else {
                                        Image(systemName: "person.fill")
                                            .font(.system(size: 25))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                            .padding(5)
                        } else {
                            // Default profile icon
                            Image(systemName: "person.circle")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 50, height: 50)
                                .clipShape(Circle())
                                .padding(5)
                        }

                        VStack(alignment: .leading) {
                            Text(userManager.currentUser.name.isEmpty ? userManager.currentUser.username : userManager.currentUser.name)
                                .font(.headline)
                                .lineLimit(1)
                            Text(userManager.currentUser.role)
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }

            Spacer()

            Button(action: {
                print("Logout button tapped")
                userManager.logout()
            }) {
                Image(systemName: "rectangle.portrait.and.arrow.right")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 30, height: 30)
                    .padding(.trailing, 20)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.leading, 20)
        .padding(.vertical, 10)
    }
}

struct SideBarView_Previews: PreviewProvider {
    static var previews: some View {
        SideBarView(selectedItem: .constant("Dashboard"), showingDetailView: .constant(false))
    }
}
