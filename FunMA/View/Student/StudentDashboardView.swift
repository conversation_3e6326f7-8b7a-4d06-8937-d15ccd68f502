import SwiftUI

struct StudentDashboardView: View {
    @StateObject private var userManager = UserManager.shared
    @StateObject private var classroomViewModel = ClassroomViewModel()
    @StateObject private var exerciseViewModel = ExerciseViewModel()
    @StateObject private var coursesViewModel = CoursesViewModel()
    @ObservedObject private var imageCache = ImageCacheManager.shared
    
    @State private var studentClassrooms: [Classroom] = []
    @State private var upcomingExercises: [Exercise] = []
    @State private var recentSubmissions: [StudentSubmission] = []
    @State private var isLoading = false
    @State private var dashboardStats = StudentDashboardStats()
    @State private var errorMessage: String?
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 20) {
                    headerSection
                    
                    if isLoading {
                        loadingView
                    } else if let error = errorMessage {
                        errorView(error)
                    } else {
                        dashboardContent
                    }
                }
                .padding()
            }
            .background(Color(.systemGray6))
            .refreshable {
                await loadDashboardData()
            }
        }
        .task {
            await loadDashboardData()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Welcome back,")
                        .font(.title2)
                        .foregroundColor(.gray)
                    
                    Text(userManager.currentUser.name.isEmpty ? userManager.currentUser.username : userManager.currentUser.name)
                        .font(.title)
                        .fontWeight(.bold)
                }
                
                Spacer()

                // Profile image or default placeholder
                if let profileImageUrl = userManager.currentUser.profileImageUrl,
                   !profileImageUrl.isEmpty {
                    CachedAsyncImage(url: profileImageUrl) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 60, height: 60)
                            .clipShape(Circle())
                    } placeholder: {
                        ZStack {
                            Circle()
                                .fill(Color.blue.opacity(0.1))
                                .frame(width: 60, height: 60)

                            if imageCache.isLoading(profileImageUrl) {
                                ProgressView()
                                    .scaleEffect(0.7)
                                    .tint(.blue)
                            } else {
                                Image(systemName: "person.fill")
                                    .font(.title2)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                } else {
                    // Default profile image placeholder
                    Circle()
                        .fill(Color.blue.opacity(0.1))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                        )
                }
            }
            
            // Quick stats row
            HStack(spacing: 20) {
                StudentQuickStatItem(
                    icon: "building.2.fill",
                    value: "\(studentClassrooms.count)",
                    label: "Classes"
                )
                
                StudentQuickStatItem(
                    icon: "doc.text.fill",
                    value: "\(upcomingExercises.count)",
                    label: "Assignments"
                )
                
                StudentQuickStatItem(
                    icon: "checkmark.circle.fill",
                    value: "\(dashboardStats.completedExercises)",
                    label: "Completed"
                )
                
                StudentQuickStatItem(
                    icon: "chart.line.uptrend.xyaxis",
                    value: "\(Int(dashboardStats.averageScore))%",
                    label: "Avg Score"
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 5)
    }
    
    // MARK: - Dashboard Content
    private var dashboardContent: some View {
        VStack(spacing: 20) {
            // Upcoming Assignments Section
            upcomingAssignmentsSection
            
            // Recent Activity Section
            recentActivitySection
            
            // Performance Overview Section
            performanceOverviewSection
            
            // Quick Actions Section
            quickActionsSection
        }
    }
    
    // MARK: - Upcoming Assignments Section
    private var upcomingAssignmentsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.orange)
                Text("Upcoming Assignments")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                
                if !upcomingExercises.isEmpty {
                    NavigationLink(destination: ExerciseTakingView()) {
                        Text("View All")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }
                }
            }
            
            if upcomingExercises.isEmpty {
                emptyAssignmentsView
            } else {
                VStack(spacing: 8) {
                    ForEach(upcomingExercises.prefix(3)) { exercise in
                        UpcomingExerciseRow(exercise: exercise)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - Recent Activity Section
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "clock.arrow.circlepath")
                    .foregroundColor(.green)
                Text("Recent Activity")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            if recentSubmissions.isEmpty {
                emptyActivityView
            } else {
                VStack(spacing: 8) {
                    ForEach(recentSubmissions.prefix(3)) { submission in
                        RecentSubmissionRow(submission: submission)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - Performance Overview Section
    private var performanceOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .foregroundColor(.purple)
                Text("Performance Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            HStack(spacing: 16) {
                VStack(spacing: 8) {
                    Text("\(dashboardStats.completedExercises)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Completed")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity)
                
                Divider()
                
                VStack(spacing: 8) {
                    Text("\(dashboardStats.pendingExercises)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Text("Pending")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity)
                
                Divider()
                
                VStack(spacing: 8) {
                    Text("\(Int(dashboardStats.averageScore))%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    Text("Average")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity)
            }
            .frame(height: 60)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "bolt.fill")
                    .foregroundColor(.blue)
                Text("Quick Actions")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            HStack(spacing: 16) {
                QuickActionButton(
                    title: "Take Exercise",
                    icon: "pencil.circle.fill",
                    color: .blue,
                    destination: AnyView(ExerciseTakingView())
                )
                QuickActionButton(
                    title: "Join Game",
                    icon: "gamecontroller.fill",
                    color: .green,
                    destination: AnyView(StudentGameJoinView())
                )
                QuickActionButton(
                    title: "AI Assistant",
                    icon: "bubble.left.and.bubble.right.fill",
                    color: .purple,
                    destination: AnyView(AIStudyAssistantView())
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    // MARK: - Loading and Error Views
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            Text("Loading your dashboard...")
                .font(.headline)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    private func errorView(_ error: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.largeTitle)
                .foregroundColor(.red)
            
            Text("Unable to load dashboard")
                .font(.headline)
            
            Text(error)
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
            
            Button("Try Again") {
                Task {
                    await loadDashboardData()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
    
    // MARK: - Empty State Views
    private var emptyAssignmentsView: some View {
        VStack(spacing: 12) {
            Image(systemName: "checkmark.circle")
                .font(.title)
                .foregroundColor(.gray)
            Text("All caught up!")
                .font(.subheadline)
                .foregroundColor(.gray)
            Text("No upcoming assignments")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, minHeight: 80)
    }
    
    private var emptyActivityView: some View {
        VStack(spacing: 12) {
            Image(systemName: "clock")
                .font(.title)
                .foregroundColor(.gray)
            Text("No recent activity")
                .font(.subheadline)
                .foregroundColor(.gray)
            Text("Complete an exercise to see activity here")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, minHeight: 80)
    }
    
    // MARK: - Data Loading
    private func loadDashboardData() async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        do {
            // Load student classrooms
            studentClassrooms = try await userManager.getStudentClassrooms()
            
            // Load student exercises
            try await exerciseViewModel.getStudentExercises()
            
            // Load recent submissions FIRST to ensure we have completion data
            try await exerciseViewModel.getAllStudentSubmissions()
            recentSubmissions = exerciseViewModel.studentSubmissions
                .sorted { $0.startTime > $1.startTime }
            
            // Filter upcoming exercises (only future due dates, not submitted)
            let now = Date()
            upcomingExercises = exerciseViewModel.exercises.filter { exercise in
                // Only show exercises that are:
                // 1. Due in the future
                // 2. Not submitted by this student (whether completed or in progress)
                let isFutureDue = exercise.dueDate.timeIntervalSince(now) > 0
                let isNotSubmitted = !hasSubmittedExercise(exercise.id)
                
                return isFutureDue && isNotSubmitted
            }.sorted { $0.dueDate < $1.dueDate }
            
            // Calculate dashboard stats
            dashboardStats = calculateDashboardStats()
            
            await MainActor.run {
                isLoading = false
            }
            
        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
                isLoading = false
            }
        }
    }
    
    private func hasCompletedExercise(_ exerciseId: UUID) -> Bool {
        exerciseViewModel.studentSubmissions.contains { submission in
            submission.exerciseId == exerciseId && 
            submission.studentId == userManager.currentUser.id &&
            submission.endTime != nil
        }
    }
    
    private func hasSubmittedExercise(_ exerciseId: UUID) -> Bool {
        exerciseViewModel.studentSubmissions.contains { submission in
            submission.exerciseId == exerciseId && 
            submission.studentId == userManager.currentUser.id
        }
    }
    
    private func calculateDashboardStats() -> StudentDashboardStats {
        let completedSubmissions = exerciseViewModel.studentSubmissions.filter { submission in
            submission.studentId == userManager.currentUser.id && submission.endTime != nil
        }
        
        let scores = completedSubmissions.compactMap { $0.score }
        let averageScore = scores.isEmpty ? 0.0 : scores.reduce(0, +) / Double(scores.count)
        
        let totalExercises = exerciseViewModel.exercises.count
        let completedExercises = completedSubmissions.count
        let pendingExercises = max(0, totalExercises - completedExercises)
        
        return StudentDashboardStats(
            completedExercises: completedExercises,
            pendingExercises: pendingExercises,
            averageScore: averageScore,
            totalClassrooms: studentClassrooms.count
        )
    }
}

// MARK: - Supporting Data Models
struct StudentDashboardStats {
    let completedExercises: Int
    let pendingExercises: Int
    let averageScore: Double
    let totalClassrooms: Int
    
    init(completedExercises: Int = 0, pendingExercises: Int = 0, averageScore: Double = 0.0, totalClassrooms: Int = 0) {
        self.completedExercises = completedExercises
        self.pendingExercises = pendingExercises
        self.averageScore = averageScore
        self.totalClassrooms = totalClassrooms
    }
}

// MARK: - Supporting Views
struct StudentQuickStatItem: View {
    let icon: String
    let value: String
    let label: String

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.blue)

            Text("\(value) \(label)")
                .font(.subheadline)
                .fontWeight(.semibold)
                .lineLimit(1)
        }
        .frame(maxWidth: .infinity)
    }
}

struct StudentClassroomCard: View {
    let classroom: Classroom
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(classroom.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                
                Image(systemName: "building.2.fill")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            
            if let subject = classroom.subject {
                Text(subject)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            
            Text("Grade \(classroom.grade.displayName)")
                .font(.caption)
                .foregroundColor(.gray)
            
            Text(classroom.schoolYear)
                .font(.caption)
                .foregroundColor(.gray)
        }
        .padding(12)
        .frame(width: 150, height: 100)
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.blue.opacity(0.3), lineWidth: 1)
        )
    }
}

struct UpcomingExerciseRow: View {
    let exercise: Exercise
    
    private var timeUntilDue: (days: Int, hours: Int) {
        let now = Date()
        let timeInterval = Int(exercise.dueDate.timeIntervalSince(now))
        let days = timeInterval / (24 * 3600)
        let hours = (timeInterval % (24 * 3600)) / 3600
        return (days: days, hours: hours)
    }
    
    private var dueDateText: String {
        let time = timeUntilDue
        
        if time.days > 0 {
            return "Due in \(time.days) day\(time.days == 1 ? "" : "s")"
        } else if time.hours > 0 {
            return "Due in \(time.hours) hour\(time.hours == 1 ? "" : "s")"
        } else {
            return "Due soon"
        }
    }
    
    private var dueDateColor: Color {
        let time = timeUntilDue
        
        if time.days == 0 && time.hours <= 6 {
            return .red
        } else if time.days <= 1 {
            return .orange
        } else if time.days <= 3 {
            return .yellow
        } else {
            return .gray
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(dueDateColor.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: "doc.text.fill")
                        .foregroundColor(dueDateColor)
                        .font(.caption)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(exercise.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(exercise.topic)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(dueDateText)
                    .font(.caption)
                    .foregroundColor(dueDateColor)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .padding(.vertical, 8)
    }
}

struct RecentSubmissionRow: View {
    let submission: StudentSubmission
    
    private var timeAgo: String {
        guard let endTime = submission.endTime else { return "In progress" }
        
        let interval = Date().timeIntervalSince(endTime)
        let hours = Int(interval) / 3600
        let days = hours / 24
        
        if days > 0 {
            return "\(days) day\(days == 1 ? "" : "s") ago"
        } else if hours > 0 {
            return "\(hours) hour\(hours == 1 ? "" : "s") ago"
        } else {
            return "Recently"
        }
    }
    
    private var calculatedScore: Double {
        // First, try to use submission's earnedPoints and totalPoints if available
        if let earnedPoints = submission.earnedPoints,
           let totalPoints = submission.totalPoints,
           totalPoints > 0 {
            return (earnedPoints / totalPoints) * 100
        }

        // Fallback to submission.score if available
        if let score = submission.score {
            return score
        }

        // If no score data is available, return -1 to indicate no score
        return -1
    }

    private var scoreColor: Color {
        let score = calculatedScore
        guard score >= 0 else { return .gray }

        if score >= 80 {
            return .green
        } else if score >= 60 {
            return .orange
        } else {
            return .red
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(scoreColor.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: submission.endTime != nil ? "checkmark" : "clock")
                        .foregroundColor(scoreColor)
                        .font(.caption)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(submission.exerciseTitle ?? "Exercise")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(timeAgo)
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            let score = calculatedScore
            if score >= 0 {
                Text("\(Int(score))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(scoreColor)
            }
        }
        .padding(.vertical, 8)
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let destination: AnyView
    
    var body: some View {
        NavigationLink(destination: destination) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(color)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct StudentDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        StudentDashboardView()
    }
} 