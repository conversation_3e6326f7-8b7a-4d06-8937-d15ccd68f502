//
//  UserProfileView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import PhotosUI
import UIKit

struct UserProfileView: View {
    @ObservedObject private var userManager = UserManager.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    
    @State private var showingLogoutAlert = false
    @State private var showingEditProfile = false
    @State private var isRefreshingUserInfo = false
    @State private var navigationDestination: NavigationDestination?
    @State private var showingImagePicker = false
    @State private var selectedProfileImage: PhotosPickerItem?
    @State private var profileImageData: Data?
    @State private var isUploadingImage = false
    @State private var showingImageCrop = false
    @State private var imageToProcess: UIImage?
    @StateObject private var alertManager = AlertManager()
    @ObservedObject private var imageCache = ImageCacheManager.shared
    @State private var refreshTrigger = 0
    @State private var currentProfileImageUrl: String = ""
    
    enum NavigationDestination: Hashable {
        case about
        case help
        case privacy
        case changePassword
    }
    
    @State private var showDownloadDataAlert = false
    @State private var showDeleteAccountAlert = false
    @State private var showDataDownloaded = false
    @State private var showAccountDeleted = false
    
    var body: some View {
        NavigationStack {
            ScrollView(.vertical, showsIndicators: true) {
                VStack(spacing: 0) {
                    headerSection
                    profileInfoSection
                    accountSettingsSection
                    actionsSection
                }
            }
            .background(backgroundColor)
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.inline)
            .navigationDestination(for: NavigationDestination.self) { destination in
                switch destination {
                case .about:
                    AboutView()
                case .help:
                    HelpSupportView()
                case .privacy:
                    PrivacySettingsView()
                case .changePassword:
                    ChangePasswordView()
                }
            }
            .navigationBarBackButtonHidden(true)
        }
        .sheet(isPresented: $showingEditProfile) {
            EditProfileView()
        }
        .photosPicker(isPresented: $showingImagePicker, selection: $selectedProfileImage, matching: .images)
        .onChange(of: selectedProfileImage) { newValue in
            print("🔄 selectedProfileImage changed: \(newValue != nil ? "image selected" : "nil")")
            print("🔄 Current showingImageCrop: \(showingImageCrop)")
            print("🔄 Current imageToProcess: \(imageToProcess != nil ? "exists" : "nil")")

            if let newValue = newValue {
                // Only process if we're not already showing the crop screen
                if !showingImageCrop {
                    Task { @MainActor in
                        print("🔄 Starting loadImageForProcessing")
                        await loadImageForProcessing(from: newValue)
                    }
                } else {
                    print("🔄 Ignoring image selection - crop screen already showing")
                }
            }
        }
        .sheet(isPresented: $showingImageCrop) {
            if let imageToProcess = imageToProcess {
                ImageCropView(
                    originalImage: imageToProcess,
                    onCropComplete: { croppedImage in
                        Task { @MainActor in
                            await processAndUploadImage(croppedImage)
                        }
                        showingImageCrop = false
                        self.imageToProcess = nil
                        self.selectedProfileImage = nil
                        print("🔄 Crop completed - reset all image states")
                    },
                    onCancel: {
                        showingImageCrop = false
                        self.imageToProcess = nil
                        self.selectedProfileImage = nil
                        print("🔄 Crop cancelled - reset all image states")
                    }
                )
                .onAppear {
                    print("🔄 ImageCropView appeared")
                }
            } else {
                Text("Error: No image to process")
                    .onAppear {
                        print("❌ Sheet presented but imageToProcess is nil")
                        showingImageCrop = false
                    }
            }
        }
        .withAlerts(alertManager)
        .alert("Logout", isPresented: $showingLogoutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Logout", role: .destructive) {
                userManager.logout()
                dismiss()
            }
        } message: {
            Text("Are you sure you want to logout?")
        }
        .onAppear {
            // Update current profile image URL
            currentProfileImageUrl = userManager.currentUser.profileImageUrl ?? ""

            // Refresh user info when view appears
            refreshUserInfo()
            // Debug current user profile image URL
            if let profileImageUrl = userManager.currentUser.profileImageUrl {
                print("🖼️ Current profile image URL: \(profileImageUrl)")
            } else {
                print("🖼️ No profile image URL set")
            }
        }
        .onChange(of: userManager.currentUser.profileImageUrl) { newUrl in
            print("🔄 Profile image URL changed: \(newUrl ?? "nil")")
            currentProfileImageUrl = newUrl ?? ""
            refreshTrigger += 1
            print("🔄 Updated currentProfileImageUrl and triggered refresh #\(refreshTrigger)")
        }
    }
    
    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(profileImageBackgroundColor)
                    .frame(width: 120, height: 120)

                // Profile image or default icon
                if let profileImageData = profileImageData,
                   let uiImage = UIImage(data: profileImageData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 120, height: 120)
                        .clipShape(Circle())
                        .onAppear {
                            print("🖼️ Displaying local profile image data")
                        }
                } else if !currentProfileImageUrl.isEmpty {
                    CachedAsyncImage(url: currentProfileImageUrl) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipShape(Circle())
                    } placeholder: {
                        ZStack {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 120, height: 120)

                            if imageCache.isLoading(currentProfileImageUrl) {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.white)
                            } else {
                                Image(systemName: "person.fill")
                                    .font(.system(size: 60))
                                    .foregroundColor(.white)
                            }
                        }
                        .onAppear {
                            print("🖼️ Loading server profile image: \(currentProfileImageUrl)")
                        }
                    }
                    .onAppear {
                        print("🖼️ CachedAsyncImage appeared for URL: \(currentProfileImageUrl)")
                    }
                    .id("cached-image-\(currentProfileImageUrl)-\(refreshTrigger)")
                } else {
                    Image(systemName: "person.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.white)
                        .onAppear {
                            print("🖼️ Displaying default profile icon")
                            print("🖼️ profileImageData: \(profileImageData != nil ? "exists" : "nil")")
                            print("🖼️ profileImageUrl: \(userManager.currentUser.profileImageUrl ?? "nil")")
                        }
                }

                // Upload button overlay
                if !userManager.currentUser.isGuest {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Button(action: {
                                showingImagePicker = true
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(Color.blue)
                                        .frame(width: 36, height: 36)

                                    if isUploadingImage {
                                        ProgressView()
                                            .scaleEffect(0.7)
                                            .tint(.white)
                                    } else {
                                        Image(systemName: "camera.fill")
                                            .font(.system(size: 16))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                            .disabled(isUploadingImage)
                            .offset(x: -8, y: -8)
                        }
                    }
                }
            }
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
            .id("profile-image-\(refreshTrigger)") // Force refresh when trigger changes
            
            VStack(spacing: 4) {
                Text(displayName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                RoleBadge(role: userManager.currentUser.role)
            }
        }
        .padding(.vertical, 30)
        .frame(maxWidth: .infinity)
        .background(headerBackgroundColor)
    }
    
    @ViewBuilder
    private var profileInfoSection: some View {
        VStack(spacing: 0) {
            SectionHeader(title: "Profile Information")
                .padding(.top, 16)
            
            VStack(spacing: 0) {
                ProfileInfoRow(
                    icon: "person.fill",
                    title: "Username",
                    value: userManager.currentUser.username,
                    iconColor: .blue
                )
                
                ProfileInfoRow(
                    icon: "textformat.abc",
                    title: "Display Name",
                    value: userManager.currentUser.name.isEmpty ? "Not set" : userManager.currentUser.name,
                    iconColor: .green
                )
                
                if let email = userManager.currentUser.email {
                    ProfileInfoRow(
                        icon: "envelope.fill",
                        title: "Email",
                        value: email,
                        iconColor: .orange
                    )
                }
                
                ProfileInfoRow(
                    icon: "creditcard.fill",
                    title: "Credits",
                    value: "\(userManager.currentUser.credit)",
                    iconColor: .purple,
                    showDivider: false
                )
            }
            .background(cardBackgroundColor)
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 20)
    }
    
    @ViewBuilder
    private var accountSettingsSection: some View {
        VStack(spacing: 0) {
            SectionHeader(title: "Account")
            
            VStack(spacing: 0) {
                if !userManager.currentUser.isGuest {
                    NavigationLink(value: NavigationDestination.changePassword) {
                        AccountSettingsRow(
                            icon: "key.fill",
                            title: "Change Password",
                            iconColor: .orange,
                            action: nil
                        )
                    }
                }
                
                AccountSettingsRow(
                    icon: "bell.fill",
                    title: "Notifications",
                    iconColor: .purple,
                    action: {
                        print("Notifications tapped")
                    }
                )
                
                NavigationLink(value: NavigationDestination.privacy) {
                    AccountSettingsRow(
                        icon: "shield.fill",
                        title: "Privacy Settings",
                        iconColor: .indigo,
                        action: nil,
                        showDivider: false
                    )
                }
            }
            .background(cardBackgroundColor)
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 20)
    }
    
    @ViewBuilder
    private var actionsSection: some View {
        VStack(spacing: 0) {
            SectionHeader(title: "Actions")
            
            VStack(spacing: 0) {
                NavigationLink(value: NavigationDestination.help) {
                    AccountSettingsRow(
                        icon: "questionmark.circle.fill",
                        title: "Help & Support",
                        iconColor: .cyan,
                        action: nil
                    )
                }
                
                NavigationLink(value: NavigationDestination.about) {
                    AccountSettingsRow(
                        icon: "info.circle.fill",
                        title: "About",
                        iconColor: .gray,
                        action: nil
                    )
                }
                
                Button(action: {
                    showingLogoutAlert = true
                }) {
                    HStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.red.opacity(0.1))
                                .frame(width: 32, height: 32)
                            
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.red)
                        }
                        
                        Text("Logout")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.red)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(cardBackgroundColor)
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 30)
    }
    
    private var displayName: String {
        if userManager.currentUser.isGuest {
            return "Guest User"
        } else if userManager.currentUser.name.isEmpty {
            return userManager.currentUser.username
        } else {
            return userManager.currentUser.name
        }
    }
    
    private var backgroundColor: Color {
        colorScheme == .dark ? Color.black : Color(UIColor.systemGroupedBackground)
    }

    private var headerBackgroundColor: Color {
        colorScheme == .dark ? Color(UIColor.systemGray6) : Color.white
    }

    private var cardBackgroundColor: Color {
        colorScheme == .dark ? Color(UIColor.systemGray6) : Color.white
    }
    
    private var profileImageBackgroundColor: Color {
        switch userManager.currentUser.role.lowercased() {
        case "student":
            return .blue
        case "teacher":
            return .green
        case "developer":
            return .purple
        default:
            return .gray
        }
    }
    
    // MARK: - Helper Methods

    private func getBaseImageUrl(_ fullUrl: String) -> String {
        // Extract base URL without query parameters for consistent caching
        if let url = URL(string: fullUrl),
           let baseUrl = URL(string: "\(url.scheme ?? "https")://\(url.host ?? "")\(url.path)") {
            return baseUrl.absoluteString
        }
        return fullUrl
    }

    private func refreshUserInfo() {
        guard !userManager.currentUser.isGuest else { return }

        isRefreshingUserInfo = true

        Task { @MainActor in
            let success = await userManager.fetchUserProfile()

            await MainActor.run {
                self.isRefreshingUserInfo = false

                if !success {
                    // Could show an error message here if needed
                    print("Failed to refresh user info")
                }
            }
        }
    }

    private func loadImageForProcessing(from item: PhotosPickerItem) async {
        print("🔄 loadImageForProcessing started")
        do {
            // Load image data from PhotosPickerItem
            guard let imageData = try await item.loadTransferable(type: Data.self) else {
                print("❌ Failed to load image data from PhotosPickerItem")
                await MainActor.run {
                    alertManager.showError("Failed to load the selected image. Please try again.")
                }
                return
            }
            print("✅ Loaded image data: \(imageData.count) bytes")

            // Validate image size (max 10MB for processing)
            let maxSize = 10 * 1024 * 1024 // 10MB
            if imageData.count > maxSize {
                await MainActor.run {
                    alertManager.showImageUploadError(.fileTooLarge(imageData.count))
                }
                return
            }

            // Create UIImage from data
            guard let uiImage = UIImage(data: imageData) else {
                await MainActor.run {
                    alertManager.showImageUploadError(.corruptedImage)
                }
                return
            }

            // Show cropping interface
            await MainActor.run {
                print("🔄 Setting imageToProcess and showingImageCrop = true")
                imageToProcess = uiImage
                showingImageCrop = true

                // Reset the selected image to prevent re-triggering
                selectedProfileImage = nil

                print("🔄 imageToProcess set: \(imageToProcess != nil ? "exists" : "nil")")
                print("🔄 showingImageCrop: \(showingImageCrop)")
                print("🔄 selectedProfileImage reset to nil")
            }

        } catch {
            await MainActor.run {
                alertManager.showError("Error loading image: \(error.localizedDescription)")
            }
        }
    }

    private func processAndUploadImage(_ image: UIImage) async {
        print("🔄 processAndUploadImage called")
        await MainActor.run {
            isUploadingImage = true
            print("🔄 Set isUploadingImage = true")
        }

        do {
            // Compress image using our utility
            guard let compressedData = await ImageCompressionUtility.compressImageAsync(
                image,
                settings: .profileImage
            ) else {
                await MainActor.run {
                    isUploadingImage = false
                    alertManager.showImageUploadError(.compressionFailed)
                }
                return
            }

            // Show compressed image as preview
            await MainActor.run {
                profileImageData = compressedData
            }

            // Upload to server
            let success = await uploadProfileImage(compressedData)

            await MainActor.run {
                isUploadingImage = false
                if success {
                    alertManager.showToast("Profile image updated successfully!")
                    // Don't clear profileImageData here - wait for profile refresh to complete
                } else {
                    // Reset image data if upload failed
                    profileImageData = nil
                }
            }

        } catch {
            await MainActor.run {
                isUploadingImage = false
                profileImageData = nil
                alertManager.showError("Failed to process image: \(error.localizedDescription)")
            }
        }
    }

    private func uploadProfileImage(_ imageData: Data) async -> Bool {
        // Convert image to base64 for API upload
        let base64String = imageData.base64EncodedString()
        let contentString = "data:image/jpeg;base64,\(base64String)"

        print("🔄 Starting profile image upload...")
        print("📊 Image size: \(imageData.count) bytes")

        let requestBody = ProfileImageUploadRequest(profileImage: contentString)

        do {
            let response: ProfileImageUploadResponse = try await userManager.api.put(
                "user/profile/image",
                body: requestBody,
                responseType: ProfileImageUploadResponse.self
            )

            print("✅ Profile image uploaded successfully")
            print("✅ Server response: \(response.message)")
            if let imageUrl = response.profileImageUrl {
                print("✅ Image URL: \(imageUrl)")

                // Cache the new image URL and clear old cache if needed
                await MainActor.run {
                    if let image = UIImage(data: imageData) {
                        // Clear any existing profile image cache for this user
                        imageCache.clearProfileImageCache(for: userManager.currentUser.id)

                        // Cache the new image
                        imageCache.cacheImage(image, forURL: imageUrl)
                        print("✅ Cached new profile image for URL: \(imageUrl)")
                    }
                }
            }

            // Refresh user profile to get updated image URL
            let refreshSuccess = await userManager.fetchUserProfile()
            if refreshSuccess {
                print("✅ User profile refreshed successfully")
                print("✅ Updated profile image URL: \(userManager.currentUser.profileImageUrl ?? "nil")")

                // Now clear the local image data so UI shows server image
                await MainActor.run {
                    print("🔄 Clearing local image data to show server image")
                    print("🔄 profileImageData before clear: \(profileImageData != nil ? "exists" : "nil")")
                    profileImageData = nil
                    print("🔄 profileImageData after clear: \(profileImageData != nil ? "exists" : "nil")")

                    // Clear the image cache to force reload with new URL
                    if let newProfileUrl = userManager.currentUser.profileImageUrl {
                        print("🔄 Clearing cache for new profile URL: \(newProfileUrl)")
                        imageCache.clearProfileImageCache(for: userManager.currentUser.id)
                    }

                    // Force UI refresh by incrementing refresh trigger
                    refreshTrigger += 1
                    print("🔄 Triggered UI refresh #\(refreshTrigger)")
                    print("🔄 Current profile URL: \(userManager.currentUser.profileImageUrl ?? "nil")")
                }
            } else {
                print("⚠️ Failed to refresh user profile after image upload")
                await MainActor.run {
                    alertManager.showWarning("Image uploaded but failed to refresh profile. Please restart the app if the image doesn't appear.")
                }
            }

            return true

        } catch {
            print("❌ Failed to upload profile image: \(error)")

            await MainActor.run {
                if let apiError = error as? APIError {
                    switch apiError {
                    case .unauthorized:
                        alertManager.showImageUploadError(.unauthorized)
                    case .serverError(let message):
                        alertManager.showImageUploadError(.serverError(message))
                    case .networkError(let message):
                        if message.contains("timeout") {
                            alertManager.showImageUploadError(.networkTimeout)
                        } else {
                            alertManager.showNetworkError(message)
                        }
                    default:
                        alertManager.showError("Upload failed: \(apiError.localizedDescription)")
                    }
                } else {
                    alertManager.showError("Upload failed: \(error.localizedDescription)")
                }
            }

            return false
        }
    }
}

struct RoleBadge: View {
    let role: String
    
    var body: some View {
        Text(role)
            .font(.caption)
            .fontWeight(.semibold)
            .padding(.horizontal, 12)
            .padding(.vertical, 4)
            .background(roleColor.opacity(0.2))
            .foregroundColor(roleColor)
            .cornerRadius(8)
    }
    
    private var roleColor: Color {
        switch role.lowercased() {
        case "student":
            return .blue
        case "teacher":
            return .green
        case "developer":
            return .purple
        default:
            return .gray
        }
    }
}

struct SectionHeader: View {
    let title: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
}

struct ProfileInfoRow: View {
    let icon: String
    let title: String
    let value: String
    let iconColor: Color
    var showDivider: Bool = true
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 32, height: 32)
                    
                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(iconColor)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    Text(value)
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                }
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            
            if showDivider {
                Divider()
                    .padding(.leading, 60)
            }
        }
    }
}

struct AccountSettingsRow: View {
    let icon: String
    let title: String
    let iconColor: Color
    let action: (() -> Void)?
    var showDivider: Bool = true
    
    var body: some View {
        VStack(spacing: 0) {
            if let action = action {
                Button(action: action) {
                    rowContent
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                rowContent
            }
            if showDivider {
                Divider()
                    .padding(.leading, 60)
            }
        }
    }
    
    private var rowContent: some View {
        HStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(iconColor.opacity(0.1))
                    .frame(width: 32, height: 32)
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(iconColor)
            }
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)
            Spacer()
            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
}

struct ChangePasswordView: View {
    @ObservedObject private var userManager = UserManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss

    @State private var oldPassword: String = ""
    @State private var newPassword: String = ""
    @State private var confirmPassword: String = ""
    @State private var isLoading: Bool = false
    @State private var errorMessage: String = ""
    @State private var showingSuccessAlert: Bool = false

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header Icon
                Image(systemName: "key.fill")
                    .resizable()
                    .frame(width: 60, height: 80)
                    .foregroundColor(.orange)

                Text("Change Password")
                    .font(.title)
                    .fontWeight(.bold)

                // Password Form
                VStack(spacing: 16) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Password")
                            .font(.headline)
                            .foregroundColor(.primary)
                        SecureField("Enter current password", text: $oldPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .textContentType(.password)
                            .onChange(of: oldPassword) { _ in
                                validatePasswords()
                            }
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("New Password")
                            .font(.headline)
                            .foregroundColor(.primary)
                        SecureField("Enter new password", text: $newPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .textContentType(.newPassword)
                            .onChange(of: newPassword) { _ in
                                validatePasswords()
                            }
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("Confirm New Password")
                            .font(.headline)
                            .foregroundColor(.primary)
                        SecureField("Confirm new password", text: $confirmPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .textContentType(.newPassword)
                            .onChange(of: confirmPassword) { _ in
                                validatePasswords()
                            }
                    }
                }
                .padding(.horizontal)

                // Error Message
                if !errorMessage.isEmpty {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .font(.subheadline)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }

                // Change Password Button
                Button(action: {
                    changePassword()
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: "key.fill")
                        }
                        Text(isLoading ? "Changing Password..." : "Change Password")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(isFormValid && !isLoading ? Color.orange : Color.gray)
                    .cornerRadius(10)
                }
                .disabled(!isFormValid || isLoading)
                .padding(.horizontal)

                Spacer()
            }
        }
        .padding()
        .navigationTitle("Change Password")
        .navigationBarTitleDisplayMode(.inline)
        .alert("Password Changed", isPresented: $showingSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Your password has been changed successfully.")
        }
        .onAppear {
            validatePasswords()
        }
    }

    private var isFormValid: Bool {
        !oldPassword.isEmpty &&
        !newPassword.isEmpty &&
        !confirmPassword.isEmpty &&
        newPassword == confirmPassword &&
        newPassword.count >= 6 &&
        errorMessage.isEmpty
    }

    private func validatePasswords() {
        errorMessage = ""

        if oldPassword.isEmpty {
            errorMessage = "Please enter your current password."
            return
        }

        if !newPassword.isEmpty && newPassword.count < 6 {
            errorMessage = "New password must be at least 6 characters long."
            return
        }

        if !confirmPassword.isEmpty && !newPassword.isEmpty && newPassword != confirmPassword {
            errorMessage = "New password and confirmation do not match."
            return
        }

        // Additional validation: ensure new password is different from old password
        if !oldPassword.isEmpty && !newPassword.isEmpty && oldPassword == newPassword {
            errorMessage = "New password must be different from your current password."
            return
        }
    }

    private func clearForm() {
        oldPassword = ""
        newPassword = ""
        confirmPassword = ""
        errorMessage = ""
    }

    private func changePassword() {
        guard isFormValid else {
            return
        }

        isLoading = true
        errorMessage = ""

        Task { @MainActor in
            do {
                let requestBody = ChangePasswordRequest(
                    old_password: oldPassword,
                    new_password: newPassword,
                    confirm_password: confirmPassword
                )

                let _: ChangePasswordResponse = try await userManager.api.post(
                    "auth/change-password",
                    body: requestBody,
                    responseType: ChangePasswordResponse.self
                )

                await MainActor.run {
                    isLoading = false
                    clearForm()
                    showingSuccessAlert = true
                }

            } catch {
                await MainActor.run {
                    isLoading = false
                    if let apiError = error as? APIError {
                        switch apiError {
                        case .unauthorized:
                            errorMessage = "Old password is incorrect."
                        case .serverError(let message):
                            errorMessage = message
                        case .decodingError(let message):
                            errorMessage = "Response error: \(message)"
                        case .networkError(let message):
                            errorMessage = "Network error: \(message)"
                        default:
                            errorMessage = "Failed to change password. Please try again."
                        }
                    } else {
                        errorMessage = error.localizedDescription
                    }
                }
            }
        }
    }
}

// MARK: - Change Password Models

struct ChangePasswordRequest: Codable {
    let old_password: String
    let new_password: String
    let confirm_password: String
}

struct ChangePasswordResponse: Codable {
    let message: String
}

// MARK: - Profile Image Upload Models

struct ProfileImageUploadRequest: Codable {
    let profileImage: String // Base64 encoded image with data URL format
}

struct ProfileImageUploadResponse: Codable {
    let message: String
    let profileImageUrl: String?
}

struct EditProfileView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var userManager = UserManager.shared
    @State private var editedName: String = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Personal Information") {
                    HStack {
                        Text("Username")
                        Spacer()
                        Text(userManager.currentUser.username)
                            .foregroundColor(.secondary)
                    }
                    
                    TextField("Display Name", text: $editedName)
                }
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        print("Save profile changes")
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            editedName = userManager.currentUser.name
        }
    }
}

#Preview {
    UserProfileView()
} 

struct AboutView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image("funma-logo")
                .resizable()
                .frame(width: 150, height: 60)
                .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
            Text("FunMA")
                .font(.title)
                .fontWeight(.bold)
            Text("Version \(appVersion)")
                .font(.headline)
                .foregroundColor(.secondary)
            Text("FunMA is an innovative platform for interactive learning and classroom management.")
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            Spacer()
        }
        .padding()
        .navigationTitle("About")
        .navigationBarTitleDisplayMode(.inline)
    }

    private var appVersion: String {
        let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        return "\(version) (\(build))"
    }
} 

struct HelpSupportView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "questionmark.circle.fill")
                .resizable()
                .frame(width: 60, height: 60)
                .foregroundColor(.cyan)
            Text("Help & Support")
                .font(.title)
                .fontWeight(.bold)
            Text("If you have any questions, feedback, or need assistance, please contact our support team.")
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "envelope.fill")
                    Text("<EMAIL>")
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                HStack {
                    Image(systemName: "message.fill")
                    Text("WhatsApp: +852 5797 9439")
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            Button(action: {
                if let url = URL(string: "mailto:<EMAIL>") {
                    UIApplication.shared.open(url)
                }
            }) {
                HStack {
                    Image(systemName: "envelope.fill")
                    Text("Email Support")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.cyan)
                .cornerRadius(10)
            }
            Button(action: {
                if let url = URL(string: "https://wa.me/85257979439") {
                    UIApplication.shared.open(url)
                }
            }) {
                HStack {
                    Image(systemName: "message.fill")
                    Text("WhatsApp Support")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.green)
                .cornerRadius(10)
            }
            Spacer()
        }
        .padding()
        .navigationTitle("Help & Support")
        .navigationBarTitleDisplayMode(.inline)
    }
} 

struct PrivacySettingsView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "shield.fill")
                .resizable()
                .frame(width: 60, height: 60)
                .foregroundColor(.indigo)
            Text("Privacy Settings")
                .font(.title)
                .fontWeight(.bold)
            Text("To download your data or delete your account, please email us at:")
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            HStack {
                Image(systemName: "envelope.fill")
                Text("<EMAIL>")
            }
            .font(.subheadline)
            .foregroundColor(.secondary)
            Button(action: {
                if let url = URL(string: "mailto:<EMAIL>?subject=Data%20Request") {
                    UIApplication.shared.open(url)
                }
            }) {
                HStack {
                    Image(systemName: "envelope.fill")
                    Text("Email Support")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.indigo)
                .cornerRadius(10)
            }
            Divider().padding(.vertical, 16)
            VStack(spacing: 12) {
                Text("Privacy Policy")
                    .font(.headline)
                    .frame(maxWidth: .infinity, alignment: .leading)
                Text("We are committed to protecting your privacy. Read our full privacy policy to learn how your data is collected, used, and protected.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                Button(action: {
                    if let url = URL(string: "https://www.inspire-edu.hk/privacy-policy") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "doc.text.fill")
                        Text("View Full Privacy Policy")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.indigo)
                    .cornerRadius(10)
                }
            }
            Spacer()
        }
        .padding()
        .navigationTitle("Privacy Settings")
        .navigationBarTitleDisplayMode(.inline)
    }
} 
