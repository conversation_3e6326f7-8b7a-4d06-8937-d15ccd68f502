//
//  ImageCropView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import UIKit

struct ImageCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void
    
    @State private var croppedImage: UIImage?
    @State private var showingCropController = false
    
    var body: some View {
        NavigationView {
            VStack {
                if let croppedImage = croppedImage {
                    // Show cropped image preview
                    VStack(spacing: 20) {
                        Text("Preview")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Image(uiImage: croppedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 2)
                            )
                        
                        Button("Crop Again") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                    }
                    .padding()
                } else {
                    // Show original image as circular preview
                    VStack(spacing: 20) {
                        Text("Profile Image Preview")
                            .font(.headline)
                            .foregroundColor(.primary)

                        // Show how the image will look as profile picture
                        Image(uiImage: originalImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 3)
                            )

                        Text("This is how your profile image will appear")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Advanced Cropping") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)

                        Spacer()
                    }
                    .padding()
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Use Image") {
                        if let croppedImage = croppedImage {
                            onCropComplete(croppedImage)
                        } else {
                            // Create a simple circular crop of the original image
                            let circularImage = createCircularCrop(from: originalImage)
                            onCropComplete(circularImage)
                        }
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCropController) {
            SimpleCropView(
                originalImage: originalImage,
                onCropComplete: { image in
                    croppedImage = image
                    showingCropController = false
                },
                onCancel: {
                    showingCropController = false
                }
            )
        }
        .onAppear {
            // Don't auto-start cropping to avoid photo library conflict
            // showingCropController = true
        }
    }

    // Helper function to create circular crop
    private func createCircularCrop(from image: UIImage) -> UIImage {
        let size = min(image.size.width, image.size.height)
        let rect = CGRect(x: (image.size.width - size) / 2,
                         y: (image.size.height - size) / 2,
                         width: size,
                         height: size)

        UIGraphicsBeginImageContextWithOptions(CGSize(width: size, height: size), false, image.scale)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()
        context?.addEllipse(in: CGRect(x: 0, y: 0, width: size, height: size))
        context?.clip()

        image.draw(at: CGPoint(x: -rect.origin.x, y: -rect.origin.y))

        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}



// Simple crop view using SwiftUI gestures (alternative implementation)
struct SimpleCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void

    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    @State private var containerSize: CGSize = .zero

    private let cropSize: CGFloat = 300
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    Color.black.ignoresSafeArea()

                    // Image
                    Image(uiImage: originalImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .gesture(
                            SimultaneousGesture(
                                MagnificationGesture()
                                    .onChanged { value in
                                        let newScale = lastScale * value
                                        scale = max(0.5, min(newScale, 4.0))
                                    }
                                    .onEnded { value in
                                        lastScale = scale
                                        // Ensure minimum scale for usability
                                        if scale < 1.0 {
                                            withAnimation(.easeOut(duration: 0.3)) {
                                                scale = 1.0
                                                lastScale = 1.0
                                            }
                                        }
                                    },
                                DragGesture()
                                    .onChanged { value in
                                        offset = CGSize(
                                            width: lastOffset.width + value.translation.width,
                                            height: lastOffset.height + value.translation.height
                                        )
                                    }
                                    .onEnded { _ in
                                        lastOffset = offset
                                    }
                            )
                        )
                    
                    // Crop overlay - circular crop area (non-interactive)
                    ZStack {
                        // Dimming overlay for the entire screen
                        // Rectangle()
                        //     .fill(Color.black.opacity(0.5))
                        //     .ignoresSafeArea()

                        // Clear circular area
                        Circle()
                            .fill(Color.clear)
                            .frame(width: cropSize, height: cropSize)
                            .blendMode(.destinationOut)
                    }
                    .compositingGroup()
                    .allowsHitTesting(false) // Allow gestures to pass through

                    // Circular border (non-interactive)
                    Circle()
                        .stroke(Color.white, lineWidth: 3)
                        .frame(width: cropSize, height: cropSize)
                        .allowsHitTesting(false) // Allow gestures to pass through

                    // Instructions
                    VStack {
                        Spacer()
                        Text("Pinch to zoom • Drag to move")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(8)
                            .padding(.bottom, 100)
                    }
                }
                .onAppear {
                    containerSize = geometry.size
                }
                .onChange(of: geometry.size) { newSize in
                    containerSize = newSize
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        cropImage()
                    }
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func cropImage() {
        guard containerSize != .zero else {
            let circularImage = createCircularCrop(from: originalImage)
            onCropComplete(circularImage)
            return
        }

        // Create a UIGraphicsImageRenderer to render what the user sees
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: cropSize, height: cropSize))

        let croppedImage = renderer.image { context in
            let cgContext = context.cgContext

            // Create circular clipping path
            cgContext.addEllipse(in: CGRect(x: 0, y: 0, width: cropSize, height: cropSize))
            cgContext.clip()

            // Calculate the image's display size and position
            let imageSize = originalImage.size
            let imageAspectRatio = imageSize.width / imageSize.height
            let containerAspectRatio = containerSize.width / containerSize.height

            // Calculate base display size (how image appears with .aspectRatio(.fit))
            var baseDisplaySize: CGSize
            if imageAspectRatio > containerAspectRatio {
                baseDisplaySize = CGSize(
                    width: containerSize.width,
                    height: containerSize.width / imageAspectRatio
                )
            } else {
                baseDisplaySize = CGSize(
                    width: containerSize.height * imageAspectRatio,
                    height: containerSize.height
                )
            }

            // Apply user's scale
            let scaledSize = CGSize(
                width: baseDisplaySize.width * scale,
                height: baseDisplaySize.height * scale
            )

            // Calculate image position in container
            let containerCenter = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
            let imageCenter = CGPoint(
                x: containerCenter.x + offset.width,
                y: containerCenter.y + offset.height
            )

            // Calculate crop area center in container coordinates
            let cropCenter = containerCenter

            // Calculate where to draw the image relative to the crop area
            let drawX = (imageCenter.x - cropCenter.x) + (cropSize / 2) - (scaledSize.width / 2)
            let drawY = (imageCenter.y - cropCenter.y) + (cropSize / 2) - (scaledSize.height / 2)

            // Draw the image
            originalImage.draw(in: CGRect(
                x: drawX,
                y: drawY,
                width: scaledSize.width,
                height: scaledSize.height
            ))
        }

        onCropComplete(croppedImage)
    }

    // Helper function to create circular crop
    private func createCircularCrop(from image: UIImage) -> UIImage {
        let size = min(image.size.width, image.size.height)
        let rect = CGRect(x: (image.size.width - size) / 2,
                         y: (image.size.height - size) / 2,
                         width: size,
                         height: size)

        UIGraphicsBeginImageContextWithOptions(CGSize(width: size, height: size), false, image.scale)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()
        context?.addEllipse(in: CGRect(x: 0, y: 0, width: size, height: size))
        context?.clip()

        image.draw(at: CGPoint(x: -rect.origin.x, y: -rect.origin.y))

        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}

#Preview {
    if let sampleImage = UIImage(systemName: "person.fill") {
        ImageCropView(
            originalImage: sampleImage,
            onCropComplete: { _ in },
            onCancel: { }
        )
    }
}
