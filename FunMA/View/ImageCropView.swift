//
//  ImageCropView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import UIKit

struct ImageCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void
    
    @State private var croppedImage: UIImage?
    @State private var showingCropController = false
    
    var body: some View {
        NavigationView {
            VStack {
                if let croppedImage = croppedImage {
                    // Show cropped image preview
                    VStack(spacing: 20) {
                        Text("Preview")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Image(uiImage: croppedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 2)
                            )
                        
                        Button("Crop Again") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                    }
                    .padding()
                } else {
                    // Show original image as circular preview
                    VStack(spacing: 20) {
                        Text("Profile Image Preview")
                            .font(.headline)
                            .foregroundColor(.primary)

                        // Show how the image will look as profile picture
                        Image(uiImage: originalImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 3)
                            )

                        Text("This is how your profile image will appear")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Advanced Cropping") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)

                        Spacer()
                    }
                    .padding()
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Use Image") {
                        if let croppedImage = croppedImage {
                            onCropComplete(croppedImage)
                        } else {
                            // Create a simple circular crop of the original image
                            let circularImage = createCircularCrop(from: originalImage)
                            onCropComplete(circularImage)
                        }
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCropController) {
            SimpleCropView(
                originalImage: originalImage,
                onCropComplete: { image in
                    croppedImage = image
                    showingCropController = false
                },
                onCancel: {
                    showingCropController = false
                }
            )
        }
        .onAppear {
            // Don't auto-start cropping to avoid photo library conflict
            // showingCropController = true
        }
    }

    // Helper function to create circular crop
    private func createCircularCrop(from image: UIImage) -> UIImage {
        let size = min(image.size.width, image.size.height)
        let rect = CGRect(x: (image.size.width - size) / 2,
                         y: (image.size.height - size) / 2,
                         width: size,
                         height: size)

        UIGraphicsBeginImageContextWithOptions(CGSize(width: size, height: size), false, image.scale)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()
        context?.addEllipse(in: CGRect(x: 0, y: 0, width: size, height: size))
        context?.clip()

        image.draw(at: CGPoint(x: -rect.origin.x, y: -rect.origin.y))

        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}



// Simple crop view using SwiftUI gestures (alternative implementation)
struct SimpleCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    
    private let cropSize: CGFloat = 300
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    Color.black.ignoresSafeArea()
                    
                    // Image
                    Image(uiImage: originalImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .gesture(
                            SimultaneousGesture(
                                MagnificationGesture()
                                    .onChanged { value in
                                        let newScale = max(0.5, min(value, 4.0))
                                        scale = newScale
                                    }
                                    .onEnded { _ in
                                        // Ensure minimum scale for usability
                                        if scale < 1.0 {
                                            withAnimation(.easeOut(duration: 0.3)) {
                                                scale = 1.0
                                            }
                                        }
                                    },
                                DragGesture()
                                    .onChanged { value in
                                        offset = CGSize(
                                            width: lastOffset.width + value.translation.width,
                                            height: lastOffset.height + value.translation.height
                                        )
                                    }
                                    .onEnded { _ in
                                        lastOffset = offset
                                    }
                            )
                        )
                    
                    // Crop overlay
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: cropSize, height: cropSize)
                        .overlay(
                            RoundedRectangle(cornerRadius: cropSize / 2)
                                .stroke(Color.white, lineWidth: 3)
                        )
                        .overlay(
                            // Dimming overlay
                            Rectangle()
                                .fill(Color.black.opacity(0.5))
                                .mask(
                                    Rectangle()
                                        .fill(Color.black)
                                        .overlay(
                                            Circle()
                                                .fill(Color.clear)
                                                .frame(width: cropSize, height: cropSize)
                                                .blendMode(.destinationOut)
                                        )
                                )
                        )

                    // Instructions
                    VStack {
                        Spacer()
                        Text("Pinch to zoom • Drag to move")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(8)
                            .padding(.bottom, 100)
                    }
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        cropImage()
                    }
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func cropImage() {
        // Calculate the actual crop area based on scale and offset
        let imageSize = originalImage.size
        let aspectRatio = imageSize.width / imageSize.height

        // Calculate the displayed image size (fit aspect ratio)
        var displayedImageSize: CGSize
        if aspectRatio > 1 {
            // Landscape - width is constrained
            displayedImageSize = CGSize(width: cropSize * 2, height: (cropSize * 2) / aspectRatio)
        } else {
            // Portrait or square - height is constrained
            displayedImageSize = CGSize(width: (cropSize * 2) * aspectRatio, height: cropSize * 2)
        }

        // Apply scale
        displayedImageSize = CGSize(
            width: displayedImageSize.width * scale,
            height: displayedImageSize.height * scale
        )

        // Calculate crop rectangle in image coordinates
        let cropRect = CGRect(
            x: max(0, (-offset.width + cropSize / 2) * (imageSize.width / displayedImageSize.width)),
            y: max(0, (-offset.height + cropSize / 2) * (imageSize.height / displayedImageSize.height)),
            width: min(imageSize.width, cropSize * (imageSize.width / displayedImageSize.width)),
            height: min(imageSize.height, cropSize * (imageSize.height / displayedImageSize.height))
        )

        // Create cropped image
        guard let cgImage = originalImage.cgImage?.cropping(to: cropRect) else {
            // Fallback to simple circular crop if calculation fails
            let circularImage = createCircularCrop(from: originalImage)
            onCropComplete(circularImage)
            return
        }

        let croppedUIImage = UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)

        // Make it circular
        let circularImage = createCircularCrop(from: croppedUIImage)
        onCropComplete(circularImage)
    }

    // Helper function to create circular crop
    private func createCircularCrop(from image: UIImage) -> UIImage {
        let size = min(image.size.width, image.size.height)
        let rect = CGRect(x: (image.size.width - size) / 2,
                         y: (image.size.height - size) / 2,
                         width: size,
                         height: size)

        UIGraphicsBeginImageContextWithOptions(CGSize(width: size, height: size), false, image.scale)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()
        context?.addEllipse(in: CGRect(x: 0, y: 0, width: size, height: size))
        context?.clip()

        image.draw(at: CGPoint(x: -rect.origin.x, y: -rect.origin.y))

        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}

#Preview {
    if let sampleImage = UIImage(systemName: "person.fill") {
        ImageCropView(
            originalImage: sampleImage,
            onCropComplete: { _ in },
            onCancel: { }
        )
    }
}
