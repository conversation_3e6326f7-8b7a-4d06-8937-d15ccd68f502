//
//  ImageCropView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import UIKit

struct ImageCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void
    
    @State private var croppedImage: UIImage?
    @State private var showingCropController = false
    
    var body: some View {
        NavigationView {
            VStack {
                if let croppedImage = croppedImage {
                    // Show cropped image preview
                    VStack(spacing: 20) {
                        Text("Preview")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Image(uiImage: croppedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 2)
                            )
                        
                        Button("Crop Again") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                    }
                    .padding()
                } else {
                    // Show original image as circular preview
                    VStack(spacing: 20) {
                        Text("Profile Image Preview")
                            .font(.headline)
                            .foregroundColor(.primary)

                        // Show how the image will look as profile picture
                        Image(uiImage: originalImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 3)
                            )

                        Text("This is how your profile image will appear")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Advanced Cropping") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)

                        Spacer()
                    }
                    .padding()
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Use Image") {
                        if let croppedImage = croppedImage {
                            onCropComplete(croppedImage)
                        } else {
                            // Create a simple circular crop of the original image
                            let circularImage = createCircularCrop(from: originalImage)
                            onCropComplete(circularImage)
                        }
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCropController) {
            ImageCropController(
                image: originalImage,
                onCropComplete: { image in
                    croppedImage = image
                    showingCropController = false
                },
                onCancel: {
                    showingCropController = false
                }
            )
        }
        .onAppear {
            // Don't auto-start cropping to avoid photo library conflict
            // showingCropController = true
        }
    }

    // Helper function to create circular crop
    private func createCircularCrop(from image: UIImage) -> UIImage {
        let size = min(image.size.width, image.size.height)
        let rect = CGRect(x: (image.size.width - size) / 2,
                         y: (image.size.height - size) / 2,
                         width: size,
                         height: size)

        UIGraphicsBeginImageContextWithOptions(CGSize(width: size, height: size), false, image.scale)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()
        context?.addEllipse(in: CGRect(x: 0, y: 0, width: size, height: size))
        context?.clip()

        image.draw(at: CGPoint(x: -rect.origin.x, y: -rect.origin.y))

        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}

struct ImageCropController: UIViewControllerRepresentable {
    let image: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.allowsEditing = true
        picker.sourceType = .camera
        
        // Set the image to be edited
        DispatchQueue.main.async {
            picker.dismiss(animated: false) {
                // Present the crop interface directly
                let cropController = UIImagePickerController()
                cropController.delegate = context.coordinator
                cropController.allowsEditing = true
                cropController.sourceType = .camera
                
                // This is a workaround to show cropping interface
                // In a real implementation, you might want to use a third-party library
                // like TOCropViewController for better cropping functionality
            }
        }
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImageCropController
        
        init(_ parent: ImageCropController) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.onCropComplete(editedImage)
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.onCropComplete(originalImage)
            }
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.onCancel()
        }
    }
}

// Simple crop view using SwiftUI gestures (alternative implementation)
struct SimpleCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    
    private let cropSize: CGFloat = 300
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    Color.black.ignoresSafeArea()
                    
                    // Image
                    Image(uiImage: originalImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .gesture(
                            SimultaneousGesture(
                                MagnificationGesture()
                                    .onChanged { value in
                                        scale = max(1.0, min(value, 3.0))
                                    },
                                DragGesture()
                                    .onChanged { value in
                                        offset = CGSize(
                                            width: lastOffset.width + value.translation.width,
                                            height: lastOffset.height + value.translation.height
                                        )
                                    }
                                    .onEnded { _ in
                                        lastOffset = offset
                                    }
                            )
                        )
                    
                    // Crop overlay
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: cropSize, height: cropSize)
                        .overlay(
                            RoundedRectangle(cornerRadius: cropSize / 2)
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .overlay(
                            // Dimming overlay
                            Rectangle()
                                .fill(Color.black.opacity(0.5))
                                .mask(
                                    Rectangle()
                                        .fill(Color.black)
                                        .overlay(
                                            Circle()
                                                .fill(Color.clear)
                                                .frame(width: cropSize, height: cropSize)
                                                .blendMode(.destinationOut)
                                        )
                                )
                        )
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        cropImage()
                    }
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func cropImage() {
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: cropSize, height: cropSize))
        let croppedImage = renderer.image { context in
            // This is a simplified crop - in a real implementation,
            // you'd need to calculate the exact crop area based on scale and offset
            originalImage.draw(in: CGRect(x: 0, y: 0, width: cropSize, height: cropSize))
        }
        onCropComplete(croppedImage)
    }
}

#Preview {
    if let sampleImage = UIImage(systemName: "person.fill") {
        ImageCropView(
            originalImage: sampleImage,
            onCropComplete: { _ in },
            onCancel: { }
        )
    }
}
