//
//  ImageCropView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import UIKit

struct ImageCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void
    
    @State private var croppedImage: UIImage?
    @State private var showingCropController = false
    
    var body: some View {
        NavigationView {
            VStack {
                if let croppedImage = croppedImage {
                    // Show cropped image preview
                    VStack(spacing: 20) {
                        Text("Preview")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Image(uiImage: croppedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 2)
                            )
                        
                        Button("Crop Again") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)
                        
                        Spacer()
                    }
                    .padding()
                } else {
                    // Show original image as circular preview
                    VStack(spacing: 20) {
                        Text("Profile Image Preview")
                            .font(.headline)
                            .foregroundColor(.primary)

                        // Show how the image will look as profile picture
                        Image(uiImage: originalImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 200, height: 200)
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(Color.blue, lineWidth: 3)
                            )

                        Text("This is how your profile image will appear")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Advanced Cropping") {
                            showingCropController = true
                        }
                        .buttonStyle(.bordered)

                        Spacer()
                    }
                    .padding()
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Use Image") {
                        if let croppedImage = croppedImage {
                            onCropComplete(croppedImage)
                        } else {
                            // Create a simple circular crop of the original image
                            let circularImage = createCircularCrop(from: originalImage)
                            onCropComplete(circularImage)
                        }
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCropController) {
            SimpleCropView(
                originalImage: originalImage,
                onCropComplete: { image in
                    croppedImage = image
                    showingCropController = false
                },
                onCancel: {
                    showingCropController = false
                }
            )
        }
        .onAppear {
            // Don't auto-start cropping to avoid photo library conflict
            // showingCropController = true
        }
    }

    // Helper function to create circular crop
    private func createCircularCrop(from image: UIImage) -> UIImage {
        let size = min(image.size.width, image.size.height)
        let rect = CGRect(x: (image.size.width - size) / 2,
                         y: (image.size.height - size) / 2,
                         width: size,
                         height: size)

        UIGraphicsBeginImageContextWithOptions(CGSize(width: size, height: size), false, image.scale)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()
        context?.addEllipse(in: CGRect(x: 0, y: 0, width: size, height: size))
        context?.clip()

        image.draw(at: CGPoint(x: -rect.origin.x, y: -rect.origin.y))

        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}



// Simple crop view using SwiftUI gestures (alternative implementation)
struct SimpleCropView: View {
    let originalImage: UIImage
    let onCropComplete: (UIImage) -> Void
    let onCancel: () -> Void

    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    @State private var containerSize: CGSize = .zero

    private let cropSize: CGFloat = 300
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    Color.black.ignoresSafeArea()

                    // Image
                    Image(uiImage: originalImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(scale)
                        .offset(offset)
                        .gesture(
                            SimultaneousGesture(
                                MagnificationGesture()
                                    .onChanged { value in
                                        let newScale = max(0.5, min(value, 4.0))
                                        scale = newScale
                                    }
                                    .onEnded { _ in
                                        // Ensure minimum scale for usability
                                        if scale < 1.0 {
                                            withAnimation(.easeOut(duration: 0.3)) {
                                                scale = 1.0
                                            }
                                        }
                                    },
                                DragGesture()
                                    .onChanged { value in
                                        offset = CGSize(
                                            width: lastOffset.width + value.translation.width,
                                            height: lastOffset.height + value.translation.height
                                        )
                                    }
                                    .onEnded { _ in
                                        lastOffset = offset
                                    }
                            )
                        )
                    
                    // Crop overlay
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: cropSize, height: cropSize)
                        .overlay(
                            RoundedRectangle(cornerRadius: cropSize / 2)
                                .stroke(Color.white, lineWidth: 3)
                        )
                        .overlay(
                            // Dimming overlay
                            Rectangle()
                                .fill(Color.black.opacity(0.5))
                                .mask(
                                    Rectangle()
                                        .fill(Color.black)
                                        .overlay(
                                            Circle()
                                                .fill(Color.clear)
                                                .frame(width: cropSize, height: cropSize)
                                                .blendMode(.destinationOut)
                                        )
                                )
                        )

                    // Instructions
                    VStack {
                        Spacer()
                        Text("Pinch to zoom • Drag to move")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(8)
                            .padding(.bottom, 100)
                    }
                }
                .onAppear {
                    containerSize = geometry.size
                }
                .onChange(of: geometry.size) { newSize in
                    containerSize = newSize
                }
            }
            .navigationTitle("Crop Image")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        cropImage()
                    }
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func cropImage() {
        guard containerSize != .zero else {
            // Fallback if container size not available
            let circularImage = createCircularCrop(from: originalImage)
            onCropComplete(circularImage)
            return
        }

        let imageSize = originalImage.size
        let imageAspectRatio = imageSize.width / imageSize.height
        let containerAspectRatio = containerSize.width / containerSize.height

        // Calculate how the image is displayed with .aspectRatio(.fit)
        var displayedImageSize: CGSize
        if imageAspectRatio > containerAspectRatio {
            // Image is wider - constrained by container width
            displayedImageSize = CGSize(
                width: containerSize.width,
                height: containerSize.width / imageAspectRatio
            )
        } else {
            // Image is taller - constrained by container height
            displayedImageSize = CGSize(
                width: containerSize.height * imageAspectRatio,
                height: containerSize.height
            )
        }

        // Apply the user's scale transformation
        let scaledDisplaySize = CGSize(
            width: displayedImageSize.width * scale,
            height: displayedImageSize.height * scale
        )

        // Calculate the center of the container
        let containerCenter = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)

        // Calculate where the scaled image's center is positioned (including user offset)
        let scaledImageCenter = CGPoint(
            x: containerCenter.x + offset.width,
            y: containerCenter.y + offset.height
        )

        // Calculate the top-left corner of the scaled image
        let scaledImageTopLeft = CGPoint(
            x: scaledImageCenter.x - scaledDisplaySize.width / 2,
            y: scaledImageCenter.y - scaledDisplaySize.height / 2
        )

        // The crop circle is always centered in the container
        let cropCenter = containerCenter
        let cropRadius = cropSize / 2

        // Calculate crop rectangle in display coordinates
        let cropRectInDisplay = CGRect(
            x: cropCenter.x - cropRadius,
            y: cropCenter.y - cropRadius,
            width: cropSize,
            height: cropSize
        )

        // Convert from display coordinates to image coordinates
        let scaleFactorX = imageSize.width / scaledDisplaySize.width
        let scaleFactorY = imageSize.height / scaledDisplaySize.height

        let cropRectInImage = CGRect(
            x: (cropRectInDisplay.origin.x - scaledImageTopLeft.x) * scaleFactorX,
            y: (cropRectInDisplay.origin.y - scaledImageTopLeft.y) * scaleFactorY,
            width: cropRectInDisplay.width * scaleFactorX,
            height: cropRectInDisplay.height * scaleFactorY
        )

        // Clamp to image bounds
        let clampedCropRect = CGRect(
            x: max(0, min(cropRectInImage.origin.x, imageSize.width - 1)),
            y: max(0, min(cropRectInImage.origin.y, imageSize.height - 1)),
            width: min(max(1, cropRectInImage.width), imageSize.width - max(0, cropRectInImage.origin.x)),
            height: min(max(1, cropRectInImage.height), imageSize.height - max(0, cropRectInImage.origin.y))
        )

        // Create cropped image
        guard clampedCropRect.width > 0 && clampedCropRect.height > 0,
              let cgImage = originalImage.cgImage?.cropping(to: clampedCropRect) else {
            // Fallback to simple circular crop if calculation fails
            let circularImage = createCircularCrop(from: originalImage)
            onCropComplete(circularImage)
            return
        }

        let croppedUIImage = UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)

        // Make it circular
        let circularImage = createCircularCrop(from: croppedUIImage)
        onCropComplete(circularImage)
    }

    // Helper function to create circular crop
    private func createCircularCrop(from image: UIImage) -> UIImage {
        let size = min(image.size.width, image.size.height)
        let rect = CGRect(x: (image.size.width - size) / 2,
                         y: (image.size.height - size) / 2,
                         width: size,
                         height: size)

        UIGraphicsBeginImageContextWithOptions(CGSize(width: size, height: size), false, image.scale)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()
        context?.addEllipse(in: CGRect(x: 0, y: 0, width: size, height: size))
        context?.clip()

        image.draw(at: CGPoint(x: -rect.origin.x, y: -rect.origin.y))

        return UIGraphicsGetImageFromCurrentImageContext() ?? image
    }
}

#Preview {
    if let sampleImage = UIImage(systemName: "person.fill") {
        ImageCropView(
            originalImage: sampleImage,
            onCropComplete: { _ in },
            onCancel: { }
        )
    }
}
