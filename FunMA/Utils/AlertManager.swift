//
//  AlertManager.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI

// MARK: - Alert Types

enum AlertType: Identifiable {
    case success(String)
    case error(String)
    case warning(String)
    case info(String)
    case imageUploadError(ImageUploadError)
    case networkError(String)
    case validationError(String)
    
    var id: String {
        switch self {
        case .success(let message): return "success_\(message)"
        case .error(let message): return "error_\(message)"
        case .warning(let message): return "warning_\(message)"
        case .info(let message): return "info_\(message)"
        case .imageUploadError(let error): return "image_error_\(error.localizedDescription)"
        case .networkError(let message): return "network_error_\(message)"
        case .validationError(let message): return "validation_error_\(message)"
        }
    }
    
    var title: String {
        switch self {
        case .success: return "Success"
        case .error: return "Error"
        case .warning: return "Warning"
        case .info: return "Information"
        case .imageUploadError: return "Upload Failed"
        case .networkError: return "Network Error"
        case .validationError: return "Invalid Input"
        }
    }
    
    var message: String {
        switch self {
        case .success(let message), .error(let message), .warning(let message), .info(let message):
            return message
        case .imageUploadError(let error):
            return error.userFriendlyMessage
        case .networkError(let message):
            return "Please check your internet connection and try again.\n\nDetails: \(message)"
        case .validationError(let message):
            return message
        }
    }
    
    var icon: String {
        switch self {
        case .success: return "checkmark.circle.fill"
        case .error: return "xmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .info: return "info.circle.fill"
        case .imageUploadError: return "photo.badge.exclamationmark"
        case .networkError: return "wifi.exclamationmark"
        case .validationError: return "exclamationmark.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .success: return .green
        case .error: return .red
        case .warning: return .orange
        case .info: return .blue
        case .imageUploadError: return .red
        case .networkError: return .orange
        case .validationError: return .red
        }
    }
}

// MARK: - Image Upload Errors

enum ImageUploadError: LocalizedError {
    case invalidFormat
    case fileTooLarge(Int)
    case compressionFailed
    case networkTimeout
    case serverError(String)
    case unauthorized
    case quotaExceeded
    case corruptedImage
    case uploadCancelled
    
    var errorDescription: String? {
        switch self {
        case .invalidFormat:
            return "Invalid image format"
        case .fileTooLarge(let size):
            return "File too large (\(ByteCountFormatter.string(fromByteCount: Int64(size), countStyle: .file)))"
        case .compressionFailed:
            return "Failed to compress image"
        case .networkTimeout:
            return "Network timeout"
        case .serverError(let message):
            return "Server error: \(message)"
        case .unauthorized:
            return "Unauthorized access"
        case .quotaExceeded:
            return "Storage quota exceeded"
        case .corruptedImage:
            return "Corrupted image file"
        case .uploadCancelled:
            return "Upload cancelled"
        }
    }
    
    var userFriendlyMessage: String {
        switch self {
        case .invalidFormat:
            return "Please select a valid image file (JPEG, PNG, or WebP)."
        case .fileTooLarge(let size):
            let sizeString = ByteCountFormatter.string(fromByteCount: Int64(size), countStyle: .file)
            return "The selected image is too large (\(sizeString)). Please choose an image smaller than 5MB or try compressing it."
        case .compressionFailed:
            return "Unable to process the image. Please try selecting a different image."
        case .networkTimeout:
            return "The upload is taking too long. Please check your internet connection and try again."
        case .serverError(let message):
            return "There was a problem with our servers. Please try again later.\n\nError: \(message)"
        case .unauthorized:
            return "You don't have permission to upload images. Please log in again."
        case .quotaExceeded:
            return "You've reached your storage limit. Please contact support to increase your quota."
        case .corruptedImage:
            return "The selected image appears to be corrupted. Please try selecting a different image."
        case .uploadCancelled:
            return "The upload was cancelled."
        }
    }
    
    var recoveryActions: [String] {
        switch self {
        case .invalidFormat:
            return ["Select a different image", "Convert image to JPEG/PNG"]
        case .fileTooLarge:
            return ["Choose a smaller image", "Compress the image", "Take a new photo"]
        case .compressionFailed:
            return ["Try a different image", "Restart the app"]
        case .networkTimeout:
            return ["Check internet connection", "Try again", "Use Wi-Fi"]
        case .serverError:
            return ["Try again later", "Contact support"]
        case .unauthorized:
            return ["Log in again", "Check account permissions"]
        case .quotaExceeded:
            return ["Delete old images", "Contact support", "Upgrade account"]
        case .corruptedImage:
            return ["Select different image", "Take new photo"]
        case .uploadCancelled:
            return ["Try uploading again"]
        }
    }
}

// MARK: - Alert Manager

@MainActor
class AlertManager: ObservableObject {
    @Published var currentAlert: AlertType?
    @Published var showingAlert = false
    
    // Toast-style notifications
    @Published var toastMessage: String?
    @Published var showingToast = false
    
    func showAlert(_ alert: AlertType) {
        currentAlert = alert
        showingAlert = true
    }
    
    func showSuccess(_ message: String) {
        showAlert(.success(message))
    }
    
    func showError(_ message: String) {
        showAlert(.error(message))
    }
    
    func showWarning(_ message: String) {
        showAlert(.warning(message))
    }
    
    func showInfo(_ message: String) {
        showAlert(.info(message))
    }
    
    func showImageUploadError(_ error: ImageUploadError) {
        showAlert(.imageUploadError(error))
    }
    
    func showNetworkError(_ message: String) {
        showAlert(.networkError(message))
    }
    
    func showValidationError(_ message: String) {
        showAlert(.validationError(message))
    }
    
    // Toast notifications for less critical messages
    func showToast(_ message: String) {
        toastMessage = message
        showingToast = true
        
        // Auto-hide after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.hideToast()
        }
    }
    
    func hideToast() {
        showingToast = false
        toastMessage = nil
    }
    
    func dismissAlert() {
        showingAlert = false
        currentAlert = nil
    }
}

// MARK: - SwiftUI View Modifiers

struct AlertModifier: ViewModifier {
    @ObservedObject var alertManager: AlertManager
    
    func body(content: Content) -> some View {
        content
            .alert(
                alertManager.currentAlert?.title ?? "Alert",
                isPresented: $alertManager.showingAlert,
                presenting: alertManager.currentAlert
            ) { alert in
                Button("OK") {
                    alertManager.dismissAlert()
                }
                
                // Add recovery actions for image upload errors
                if case .imageUploadError(let error) = alert {
                    ForEach(error.recoveryActions.prefix(2), id: \.self) { action in
                        Button(action) {
                            // Handle recovery actions
                            alertManager.dismissAlert()
                        }
                    }
                }
            } message: { alert in
                Text(alert.message)
            }
            .overlay(
                // Toast notification overlay
                VStack {
                    if alertManager.showingToast, let message = alertManager.toastMessage {
                        ToastView(message: message)
                            .transition(.move(edge: .top).combined(with: .opacity))
                            .animation(.spring(), value: alertManager.showingToast)
                    }
                    Spacer()
                }
                .allowsHitTesting(false)
            )
    }
}

struct ToastView: View {
    let message: String
    
    var body: some View {
        Text(message)
            .font(.subheadline)
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color.black.opacity(0.8))
            .cornerRadius(8)
            .padding(.horizontal, 20)
            .padding(.top, 10)
    }
}

extension View {
    func withAlerts(_ alertManager: AlertManager) -> some View {
        modifier(AlertModifier(alertManager: alertManager))
    }
}

// MARK: - Specialized Alert Views

struct ImageUploadErrorAlert: View {
    let error: ImageUploadError
    let onDismiss: () -> Void
    let onRetry: (() -> Void)?
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo.badge.exclamationmark")
                .font(.system(size: 50))
                .foregroundColor(.red)
            
            Text("Upload Failed")
                .font(.title2)
                .fontWeight(.bold)
            
            Text(error.userFriendlyMessage)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            VStack(spacing: 12) {
                if let onRetry = onRetry {
                    Button("Try Again") {
                        onRetry()
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                Button("Cancel") {
                    onDismiss()
                }
                .buttonStyle(.bordered)
            }
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 10)
    }
}
