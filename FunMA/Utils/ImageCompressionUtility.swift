//
//  ImageCompressionUtility.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import UIKit
import SwiftUI

struct ImageCompressionUtility {
    
    // MARK: - Compression Settings
    
    struct CompressionSettings {
        let maxWidth: CGFloat
        let maxHeight: CGFloat
        let jpegQuality: CGFloat
        let maxFileSize: Int // in bytes
        
        static let profileImage = CompressionSettings(
            maxWidth: 300,
            maxHeight: 300,
            jpegQuality: 0.8,
            maxFileSize: 1024 * 1024 // 1MB
        )
        
        static let highQuality = CompressionSettings(
            maxWidth: 800,
            maxHeight: 800,
            jpegQuality: 0.9,
            maxFileSize: 2 * 1024 * 1024 // 2MB
        )
        
        static let lowQuality = CompressionSettings(
            maxWidth: 200,
            maxHeight: 200,
            jpegQuality: 0.6,
            maxFileSize: 512 * 1024 // 512KB
        )
    }
    
    // MARK: - Compression Methods
    
    /// Compress image with specified settings
    static func compressImage(_ image: UIImage, settings: CompressionSettings = .profileImage) -> Data? {
        print("🔄 Starting image compression...")
        print("📊 Original image size: \(image.size)")
        
        // Step 1: Resize image if needed
        let resizedImage = resizeImage(image, maxWidth: settings.maxWidth, maxHeight: settings.maxHeight)
        print("📊 Resized image size: \(resizedImage.size)")
        
        // Step 2: Convert to JPEG with quality compression
        guard var compressedData = resizedImage.jpegData(compressionQuality: settings.jpegQuality) else {
            print("❌ Failed to convert image to JPEG")
            return nil
        }
        
        print("📊 Initial compressed size: \(compressedData.count) bytes")
        
        // Step 3: Further compress if still too large
        if compressedData.count > settings.maxFileSize {
            compressedData = compressToTargetSize(resizedImage, targetSize: settings.maxFileSize) ?? compressedData
        }
        
        print("✅ Final compressed size: \(compressedData.count) bytes")
        print("📉 Compression ratio: \(String(format: "%.1f", Double(compressedData.count) / Double(getImageDataSize(image)) * 100))%")
        
        return compressedData
    }
    
    /// Resize image to fit within specified dimensions while maintaining aspect ratio
    static func resizeImage(_ image: UIImage, maxWidth: CGFloat, maxHeight: CGFloat) -> UIImage {
        let size = image.size
        
        // Calculate scaling factor
        let widthRatio = maxWidth / size.width
        let heightRatio = maxHeight / size.height
        let scaleFactor = min(widthRatio, heightRatio, 1.0) // Don't upscale
        
        let newSize = CGSize(
            width: size.width * scaleFactor,
            height: size.height * scaleFactor
        )
        
        // Create new image with calculated size
        let renderer = UIGraphicsImageRenderer(size: newSize)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }
    
    /// Compress image to target file size by adjusting quality
    static func compressToTargetSize(_ image: UIImage, targetSize: Int, minQuality: CGFloat = 0.1) -> Data? {
        var quality: CGFloat = 0.9
        var compressedData: Data?
        
        while quality >= minQuality {
            compressedData = image.jpegData(compressionQuality: quality)
            
            if let data = compressedData, data.count <= targetSize {
                print("🎯 Achieved target size with quality: \(quality)")
                return data
            }
            
            quality -= 0.1
        }
        
        print("⚠️ Could not achieve target size, returning best effort")
        return compressedData
    }
    
    /// Get estimated data size of UIImage
    static func getImageDataSize(_ image: UIImage) -> Int {
        // Estimate based on image dimensions and color depth
        let width = Int(image.size.width * image.scale)
        let height = Int(image.size.height * image.scale)
        return width * height * 4 // 4 bytes per pixel for RGBA
    }
    
    /// Create thumbnail from image
    static func createThumbnail(_ image: UIImage, size: CGSize = CGSize(width: 100, height: 100)) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
    }
    
    /// Compress image with progressive quality reduction
    static func progressiveCompress(_ image: UIImage, maxFileSize: Int) -> (data: Data?, quality: CGFloat) {
        let qualities: [CGFloat] = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
        
        for quality in qualities {
            if let data = image.jpegData(compressionQuality: quality),
               data.count <= maxFileSize {
                print("✅ Progressive compression achieved with quality: \(quality)")
                return (data, quality)
            }
        }
        
        // Return lowest quality if nothing works
        let finalData = image.jpegData(compressionQuality: 0.1)
        return (finalData, 0.1)
    }
    
    /// Optimize image for profile picture use
    static func optimizeForProfile(_ image: UIImage) -> Data? {
        // Specific optimization for profile pictures
        let settings = CompressionSettings(
            maxWidth: 300,
            maxHeight: 300,
            jpegQuality: 0.85,
            maxFileSize: 800 * 1024 // 800KB
        )
        
        // Ensure image is square for profile pictures
        let squareImage = makeSquare(image)
        return compressImage(squareImage, settings: settings)
    }
    
    /// Make image square by cropping to center
    static func makeSquare(_ image: UIImage) -> UIImage {
        let size = image.size
        let minDimension = min(size.width, size.height)
        
        let cropRect = CGRect(
            x: (size.width - minDimension) / 2,
            y: (size.height - minDimension) / 2,
            width: minDimension,
            height: minDimension
        )
        
        guard let cgImage = image.cgImage?.cropping(to: cropRect) else {
            return image
        }
        
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
    }
}

// MARK: - SwiftUI Extensions

extension Image {
    /// Create compressed image from UIImage
    init(compressedUIImage uiImage: UIImage, settings: ImageCompressionUtility.CompressionSettings = .profileImage) {
        if let compressedData = ImageCompressionUtility.compressImage(uiImage, settings: settings),
           let compressedUIImage = UIImage(data: compressedData) {
            self.init(uiImage: compressedUIImage)
        } else {
            self.init(uiImage: uiImage)
        }
    }
}

// MARK: - Compression Quality Indicator

struct CompressionInfo {
    let originalSize: Int
    let compressedSize: Int
    let quality: CGFloat
    let compressionRatio: Double
    
    var compressionPercentage: String {
        return String(format: "%.1f%%", compressionRatio * 100)
    }
    
    var sizeSaved: String {
        let saved = originalSize - compressedSize
        return ByteCountFormatter.string(fromByteCount: Int64(saved), countStyle: .file)
    }
}

// MARK: - Async Compression

extension ImageCompressionUtility {
    /// Compress image asynchronously
    static func compressImageAsync(_ image: UIImage, settings: CompressionSettings = .profileImage) async -> Data? {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let result = compressImage(image, settings: settings)
                continuation.resume(returning: result)
            }
        }
    }
    
    /// Batch compress multiple images
    static func compressImagesAsync(_ images: [UIImage], settings: CompressionSettings = .profileImage) async -> [Data?] {
        return await withTaskGroup(of: (Int, Data?).self) { group in
            for (index, image) in images.enumerated() {
                group.addTask {
                    let compressed = await compressImageAsync(image, settings: settings)
                    return (index, compressed)
                }
            }
            
            var results: [Data?] = Array(repeating: nil, count: images.count)
            for await (index, data) in group {
                results[index] = data
            }
            return results
        }
    }
}
