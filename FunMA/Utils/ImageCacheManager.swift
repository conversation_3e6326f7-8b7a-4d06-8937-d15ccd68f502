//
//  ImageCacheManager.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import UIKit
import SwiftUI
import Combine

@MainActor
class ImageCacheManager: ObservableObject {
    static let shared = ImageCacheManager()
    
    // MARK: - Properties
    
    private let memoryCache = NSCache<NSString, UIImage>()
    private let diskCacheURL: URL
    private let maxDiskCacheSize: Int = 100 * 1024 * 1024 // 100MB
    private let maxMemoryCacheSize: Int = 50 * 1024 * 1024 // 50MB
    private let cacheExpirationTime: TimeInterval = 7 * 24 * 60 * 60 // 7 days
    
    @Published private var loadingURLs: Set<String> = []
    
    // MARK: - Initialization
    
    private init() {
        // Setup disk cache directory
        let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        diskCacheURL = cacheDirectory.appendingPathComponent("ImageCache")
        
        // Create cache directory if it doesn't exist
        try? FileManager.default.createDirectory(at: diskCacheURL, withIntermediateDirectories: true)
        
        // Configure memory cache
        memoryCache.totalCostLimit = maxMemoryCacheSize
        memoryCache.countLimit = 100 // Max 100 images in memory
        
        // Setup cache cleanup
        setupCacheCleanup()
        
        print("🗂️ ImageCacheManager initialized")
        print("📁 Disk cache location: \(diskCacheURL.path)")
    }
    
    // MARK: - Public Methods
    
    /// Get image from cache or download if not available
    func getImage(from urlString: String) async -> UIImage? {
        guard let url = URL(string: urlString) else {
            print("❌ Invalid URL: \(urlString)")
            return nil
        }
        
        let cacheKey = cacheKeyForURL(urlString)
        
        // Check memory cache first
        if let cachedImage = memoryCache.object(forKey: cacheKey as NSString) {
            print("✅ Image found in memory cache: \(urlString)")
            return cachedImage
        }
        
        // Check disk cache
        if let diskImage = loadImageFromDisk(cacheKey: cacheKey) {
            print("✅ Image found in disk cache: \(urlString)")
            // Store in memory cache for faster access
            memoryCache.setObject(diskImage, forKey: cacheKey as NSString)
            return diskImage
        }
        
        // Download image if not in cache
        return await downloadAndCacheImage(from: url, cacheKey: cacheKey)
    }
    
    /// Cache image with URL key
    func cacheImage(_ image: UIImage, forURL urlString: String) {
        let cacheKey = cacheKeyForURL(urlString)
        
        // Store in memory cache
        memoryCache.setObject(image, forKey: cacheKey as NSString)
        
        // Store in disk cache
        Task {
            await saveImageToDisk(image, cacheKey: cacheKey)
        }
        
        print("💾 Cached image for URL: \(urlString)")
    }
    
    /// Check if image is currently being loaded
    func isLoading(_ urlString: String) -> Bool {
        return loadingURLs.contains(urlString)
    }
    
    /// Clear all cached images
    func clearCache() {
        memoryCache.removeAllObjects()
        
        Task {
            do {
                let files = try FileManager.default.contentsOfDirectory(at: diskCacheURL, includingPropertiesForKeys: nil)
                for file in files {
                    try FileManager.default.removeItem(at: file)
                }
                print("🗑️ Cleared all cached images")
            } catch {
                print("❌ Failed to clear disk cache: \(error)")
            }
        }
    }
    
    /// Get cache size information
    func getCacheInfo() -> (memorySize: Int, diskSize: Int, imageCount: Int) {
        let memorySize = memoryCache.totalCostLimit
        
        var diskSize = 0
        var imageCount = 0
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: diskCacheURL, includingPropertiesForKeys: [.fileSizeKey])
            imageCount = files.count
            
            for file in files {
                let resources = try file.resourceValues(forKeys: [.fileSizeKey])
                diskSize += resources.fileSize ?? 0
            }
        } catch {
            print("❌ Failed to calculate cache size: \(error)")
        }
        
        return (memorySize, diskSize, imageCount)
    }
    
    // MARK: - Private Methods
    
    private func downloadAndCacheImage(from url: URL, cacheKey: String) async -> UIImage? {
        let urlString = url.absoluteString
        
        // Prevent multiple downloads of the same image
        if loadingURLs.contains(urlString) {
            // Wait for existing download to complete
            while loadingURLs.contains(urlString) {
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
            }
            // Try to get from cache again
            return memoryCache.object(forKey: cacheKey as NSString) ?? loadImageFromDisk(cacheKey: cacheKey)
        }
        
        loadingURLs.insert(urlString)
        defer { loadingURLs.remove(urlString) }
        
        print("⬇️ Downloading image: \(urlString)")
        
        do {
            let (data, response) = try await URLSession.shared.data(from: url)
            
            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200,
                  let image = UIImage(data: data) else {
                print("❌ Failed to download or create image from: \(urlString)")
                return nil
            }
            
            // Cache the downloaded image
            memoryCache.setObject(image, forKey: cacheKey as NSString)
            await saveImageToDisk(image, cacheKey: cacheKey)
            
            print("✅ Downloaded and cached image: \(urlString)")
            return image
            
        } catch {
            print("❌ Download error for \(urlString): \(error)")
            return nil
        }
    }
    
    private func loadImageFromDisk(cacheKey: String) -> UIImage? {
        let fileURL = diskCacheURL.appendingPathComponent(cacheKey)
        
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            return nil
        }
        
        // Check if file is expired
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
            if let modificationDate = attributes[.modificationDate] as? Date,
               Date().timeIntervalSince(modificationDate) > cacheExpirationTime {
                // File is expired, remove it
                try FileManager.default.removeItem(at: fileURL)
                return nil
            }
        } catch {
            print("❌ Failed to check file attributes: \(error)")
        }
        
        return UIImage(contentsOfFile: fileURL.path)
    }
    
    private func saveImageToDisk(_ image: UIImage, cacheKey: String) async {
        guard let data = image.jpegData(compressionQuality: 0.8) else {
            print("❌ Failed to convert image to data for caching")
            return
        }
        
        let fileURL = diskCacheURL.appendingPathComponent(cacheKey)
        
        do {
            try data.write(to: fileURL)
            print("💾 Saved image to disk: \(cacheKey)")
        } catch {
            print("❌ Failed to save image to disk: \(error)")
        }
        
        // Clean up old files if cache is too large
        await cleanupDiskCacheIfNeeded()
    }
    
    private func cacheKeyForURL(_ urlString: String) -> String {
        // Create a safe filename from URL
        return urlString.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? UUID().uuidString
    }
    
    private func setupCacheCleanup() {
        // Clean up expired files on app launch
        Task {
            await cleanupExpiredFiles()
        }
        
        // Setup periodic cleanup (every hour when app is active)
        Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { _ in
            Task { @MainActor in
                await self.cleanupExpiredFiles()
            }
        }
    }
    
    private func cleanupExpiredFiles() async {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: diskCacheURL, includingPropertiesForKeys: [.contentModificationDateKey])
            let now = Date()

            for file in files {
                let resources = try file.resourceValues(forKeys: [.contentModificationDateKey])
                if let modificationDate = resources.contentModificationDate,
                   now.timeIntervalSince(modificationDate) > cacheExpirationTime {
                    try FileManager.default.removeItem(at: file)
                    print("🗑️ Removed expired cache file: \(file.lastPathComponent)")
                }
            }
        } catch {
            print("❌ Failed to cleanup expired files: \(error)")
        }
    }
    
    private func cleanupDiskCacheIfNeeded() async {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: diskCacheURL, includingPropertiesForKeys: [.fileSizeKey, .contentModificationDateKey])

            var totalSize = 0
            var fileInfos: [(url: URL, size: Int, date: Date)] = []

            for file in files {
                let resources = try file.resourceValues(forKeys: [.fileSizeKey, .contentModificationDateKey])
                let size = resources.fileSize ?? 0
                let date = resources.contentModificationDate ?? Date.distantPast

                totalSize += size
                fileInfos.append((url: file, size: size, date: date))
            }
            
            if totalSize > maxDiskCacheSize {
                // Sort by modification date (oldest first)
                fileInfos.sort { $0.date < $1.date }
                
                // Remove oldest files until under limit
                for fileInfo in fileInfos {
                    if totalSize <= maxDiskCacheSize * 3 / 4 { // Remove until 75% of limit
                        break
                    }
                    
                    try FileManager.default.removeItem(at: fileInfo.url)
                    totalSize -= fileInfo.size
                    print("🗑️ Removed old cache file: \(fileInfo.url.lastPathComponent)")
                }
            }
        } catch {
            print("❌ Failed to cleanup disk cache: \(error)")
        }
    }
}

// MARK: - SwiftUI Integration

struct CachedAsyncImage<Content: View, Placeholder: View>: View {
    let url: String
    let content: (Image) -> Content
    let placeholder: () -> Placeholder

    @ObservedObject private var cacheManager = ImageCacheManager.shared
    @State private var image: UIImage?
    @State private var isLoading = false

    init(
        url: String,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.content = content
        self.placeholder = placeholder
    }

    var body: some View {
        Group {
            if let image = image {
                content(Image(uiImage: image))
            } else {
                placeholder()
                    .overlay(
                        Group {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                        }
                    )
            }
        }
        .onAppear {
            loadImage()
        }
        .onChange(of: url) { _ in
            loadImage()
        }
    }

    private func loadImage() {
        guard !url.isEmpty else { return }

        isLoading = true

        Task {
            let loadedImage = await cacheManager.getImage(from: url)

            await MainActor.run {
                self.image = loadedImage
                self.isLoading = false
            }
        }
    }
}

// Convenience initializer for simple use cases
extension CachedAsyncImage where Content == Image, Placeholder == Color {
    init(url: String) {
        self.init(
            url: url,
            content: { $0 },
            placeholder: { Color.gray.opacity(0.3) }
        )
    }
}

// MARK: - Profile Image Cache Extension

extension ImageCacheManager {
    /// Specialized method for profile images
    func getProfileImage(for userId: String, from urlString: String?) async -> UIImage? {
        guard let urlString = urlString, !urlString.isEmpty else {
            return nil
        }

        // Use user ID as additional cache key for profile images
        let profileCacheKey = "profile_\(userId)_\(cacheKeyForURL(urlString))"

        // Check if we have a cached profile image for this user
        if let cachedImage = memoryCache.object(forKey: profileCacheKey as NSString) {
            return cachedImage
        }

        // Get image from regular cache or download
        if let image = await getImage(from: urlString) {
            // Cache with profile-specific key
            memoryCache.setObject(image, forKey: profileCacheKey as NSString)
            return image
        }

        return nil
    }

    /// Clear profile image cache for specific user
    func clearProfileImageCache(for userId: String) {
        // Since NSCache doesn't provide allKeys, we'll use a different approach
        // Store profile cache keys separately for tracking
        let profilePrefix = "profile_\(userId)_"

        // For now, we'll clear the entire memory cache when clearing profile cache
        // In a production app, you might want to maintain a separate tracking mechanism
        memoryCache.removeAllObjects()

        print("🗑️ Cleared profile image cache for user: \(userId)")
    }
}
