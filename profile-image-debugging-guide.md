# Profile Image Upload Debugging Guide

## Issue
Profile images are successfully uploaded to the server but not displaying in the UserProfileView.

## Changes Made to Fix the Issue

### 1. Enhanced AsyncImage Error Handling
**File:** `UserProfileView.swift`
- Added comprehensive error handling for AsyncImage loading
- Added debug logging to see what URLs are being loaded
- Added fallback handling for different loading states

### 2. Fixed UserManager Profile Fetching
**File:** `UserManager.swift`
- Added missing `profileImageUrl` field in manual JSON parsing
- Added debug logging to track profile image URL updates

### 3. Enhanced Upload Process Debugging
**File:** `UserProfileView.swift`
- Added detailed logging for upload process
- Clear local image data after successful upload
- Better error handling and response logging

## Debugging Steps

### Step 1: Check Console Logs
When you upload a profile image, look for these log messages:

```
🔄 Starting profile image upload...
📊 Image size: [size] bytes
🌐 APIService: PUT [URL]
📤 APIService: Request body: [JSON]
✅ Profile image uploaded successfully
✅ Server response: [message]
✅ Image URL: [URL]
🔍 UserManager: Fetching user profile for user: [user_id]
🌐 UserManager: Profile response status: 200
🔍 UserManager: Raw profile response: [JSON]
🖼️ UserManager: Profile Image URL: [URL]
```

### Step 2: Verify Backend Response
Check that your backend is returning the correct response format:

**Upload Response (`PUT /api/user/profile/image`):**
```json
{
  "message": "Profile image uploaded successfully",
  "profileImageUrl": "https://your-signed-url.com/image.jpg"
}
```

**Profile Response (`GET /api/user/profile`):**
```json
{
  "_id": "user_id",
  "username": "username",
  "profileImageUrl": "https://your-signed-url.com/image.jpg",
  // ... other fields
}
```

### Step 3: Check Image URL Validity
The most common issues are:

1. **Expired Signed URLs**: S3 signed URLs expire after the specified time
2. **CORS Issues**: Make sure your S3 bucket allows cross-origin requests
3. **URL Format**: Ensure the URL is properly formatted and accessible

### Step 4: Test Image URL Manually
Copy the profileImageUrl from the logs and test it:
1. Paste it in a web browser - it should display the image
2. Check if it's accessible from your app's domain
3. Verify the URL doesn't contain any encoding issues

## Common Issues and Solutions

### Issue 1: Image URL is null or empty
**Symptoms:** Log shows `🖼️ UserManager: Profile Image URL: nil`
**Solution:** Check backend database - ensure `profileImageUrl` field is being saved

### Issue 2: Image URL exists but AsyncImage fails to load
**Symptoms:** Log shows `❌ Failed to load profile image: [error]`
**Solutions:**
- Check CORS configuration on your S3 bucket
- Verify the signed URL hasn't expired
- Test the URL in a web browser

### Issue 3: Upload succeeds but profile doesn't refresh
**Symptoms:** Upload logs show success but image doesn't appear
**Solution:** Check if `fetchUserProfile()` is being called and succeeding

### Issue 4: Signed URL expires quickly
**Symptoms:** Image loads initially but disappears after some time
**Solution:** 
- Increase expiration time in backend (currently 24 hours)
- Implement URL refresh mechanism
- Consider using permanent URLs with proper access controls

## Backend Verification Checklist

1. **Database Update**: Verify that `profileImageUrl` is being saved to the user document
2. **S3 Upload**: Confirm the image is actually uploaded to S3
3. **Signed URL Generation**: Ensure `generate_presigned_url()` is working correctly
4. **CORS Configuration**: S3 bucket must allow cross-origin requests from your app

## Frontend Testing Commands

Add these temporary debug buttons to test specific functionality:

```swift
// Add to UserProfileView for testing
Button("Debug: Print Profile URL") {
    print("Current profile URL: \(userManager.currentUser.profileImageUrl ?? "nil")")
}

Button("Debug: Force Refresh") {
    Task {
        let success = await userManager.fetchUserProfile()
        print("Refresh result: \(success)")
    }
}

Button("Debug: Test Image URL") {
    if let url = userManager.currentUser.profileImageUrl {
        print("Testing URL: \(url)")
        // You can also open this URL in Safari for testing
    }
}
```

## Expected Behavior

1. User taps camera button
2. Selects image from photo library
3. Image appears immediately as preview (using local data)
4. Upload starts with progress indicator
5. Upload completes successfully
6. Profile refreshes from server
7. Local preview is cleared
8. Server image loads via AsyncImage
9. Image persists across app restarts

## Next Steps

1. Run the app and upload a profile image
2. Check the console logs for the debug messages
3. Verify each step is working as expected
4. If AsyncImage is failing, test the URL manually in a browser
5. Check your S3 bucket CORS configuration if needed

## S3 CORS Configuration Example

If images aren't loading due to CORS issues, add this to your S3 bucket:

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "HEAD"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": []
    }
]
```
