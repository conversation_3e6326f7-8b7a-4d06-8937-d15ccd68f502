# Profile Image Upload Backend Implementation

## Overview
This document outlines the backend implementation needed to support profile image upload functionality in the FunMA app.

## Database Schema Changes

### User Collection Updates
Add a new field to the existing user collection:

```javascript
// MongoDB User Document Schema
{
  _id: ObjectId,
  username: String,
  password: String, // hashed
  firstName: String,
  lastName: String,
  email: String,
  phone: String,
  gender: String,
  grade: String,
  role: String,
  credit: Number,
  school_id: ObjectId,
  profileImageUrl: String, // NEW FIELD - URL to the uploaded image
  createdAt: Date,
  updatedAt: Date
}
```

## API Endpoints

### 1. Upload Profile Image
**Endpoint:** `PUT /api/user/profile/image`
**Authentication:** Required (JWT token)
**Content-Type:** `application/json`

**Request Body:**
```json
{
  "profileImage": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..." // Base64 encoded image with data URL format
}
```

**Response:**
```json
{
  "message": "Profile image uploaded successfully",
  "profileImageUrl": "https://your-cdn.com/profile-images/user-id-timestamp.jpg"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid image format or size too large
- `401 Unauthorized`: Invalid or missing JWT token
- `413 Payload Too Large`: Image exceeds size limit
- `500 Internal Server Error`: Server error during upload

### 2. Get User Profile (Updated)
**Endpoint:** `GET /api/user/profile`
**Authentication:** Required (JWT token)

**Response (Updated to include profileImageUrl):**
```json
{
  "_id": "507f1f77bcf86cd799439011",
  "username": "john_doe",
  "name": "John Doe",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "role": "Student",
  "credit": 100,
  "school_id": "507f1f77bcf86cd799439012",
  "profileImageUrl": "https://your-cdn.com/profile-images/user-id-timestamp.jpg"
}
```

## Implementation Details

### 1. Image Processing Requirements
- **Supported formats:** JPEG, PNG, WebP
- **Maximum file size:** 5MB
- **Image processing:** Resize to 300x300px, compress to reduce file size
- **Storage:** AWS S3, Google Cloud Storage, or similar CDN

### 2. Security Considerations
- Validate image format and content (prevent malicious uploads)
- Scan for malware using services like ClamAV
- Rate limiting: Max 5 uploads per user per hour
- File size validation on both client and server
- Sanitize file names and use UUIDs for storage

### 3. File Storage Strategy
```
Storage Path: /profile-images/{userId}/{timestamp}-{uuid}.{extension}
Example: /profile-images/507f1f77bcf86cd799439011/1642694400000-a1b2c3d4.jpg
```

### 4. Database Operations
```javascript
// Update user profile image URL
await User.findByIdAndUpdate(
  userId,
  { 
    profileImageUrl: imageUrl,
    updatedAt: new Date()
  },
  { new: true }
);
```

## Node.js/Express Implementation Example

### Dependencies
```json
{
  "multer": "^1.4.5-lts.1",
  "sharp": "^0.32.6",
  "aws-sdk": "^2.1490.0",
  "uuid": "^9.0.1"
}
```

### Route Handler
```javascript
const express = require('express');
const multer = require('multer');
const sharp = require('sharp');
const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const User = require('../models/User');
const auth = require('../middleware/auth');

const router = express.Router();

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

// PUT /api/user/profile/image
router.put('/profile/image', auth, async (req, res) => {
  try {
    const { profileImage } = req.body;
    
    if (!profileImage || !profileImage.startsWith('data:image/')) {
      return res.status(400).json({ error: 'Invalid image format' });
    }
    
    // Extract base64 data
    const base64Data = profileImage.split(',')[1];
    const imageBuffer = Buffer.from(base64Data, 'base64');
    
    // Validate file size (5MB limit)
    if (imageBuffer.length > 5 * 1024 * 1024) {
      return res.status(413).json({ error: 'Image too large. Maximum size is 5MB.' });
    }
    
    // Process image with Sharp
    const processedImage = await sharp(imageBuffer)
      .resize(300, 300, { fit: 'cover' })
      .jpeg({ quality: 85 })
      .toBuffer();
    
    // Generate unique filename
    const fileName = `${req.user.id}/${Date.now()}-${uuidv4()}.jpg`;
    const key = `profile-images/${fileName}`;
    
    // Upload to S3
    const uploadParams = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: key,
      Body: processedImage,
      ContentType: 'image/jpeg',
      ACL: 'public-read'
    };
    
    const uploadResult = await s3.upload(uploadParams).promise();
    const imageUrl = uploadResult.Location;
    
    // Update user in database
    const updatedUser = await User.findByIdAndUpdate(
      req.user.id,
      { 
        profileImageUrl: imageUrl,
        updatedAt: new Date()
      },
      { new: true }
    );
    
    res.json({
      message: 'Profile image uploaded successfully',
      profileImageUrl: imageUrl
    });
    
  } catch (error) {
    console.error('Profile image upload error:', error);
    res.status(500).json({ error: 'Failed to upload profile image' });
  }
});

module.exports = router;
```

### Environment Variables
```env
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket-name
```

## Testing

### Test Cases
1. **Valid image upload** - JPEG, PNG under 5MB
2. **Invalid format** - Non-image files, unsupported formats
3. **File size limits** - Images over 5MB should be rejected
4. **Authentication** - Unauthenticated requests should fail
5. **Rate limiting** - Multiple rapid uploads should be throttled
6. **Database consistency** - Profile URL should be updated correctly

### Sample Test Data
```javascript
// Valid base64 image for testing
const testImage = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...";

// Test request
const response = await fetch('/api/user/profile/image', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({ profileImage: testImage })
});
```

## Deployment Considerations

1. **CDN Configuration:** Set up CloudFront or similar for fast image delivery
2. **Backup Strategy:** Regular S3 backups and versioning
3. **Monitoring:** Track upload success rates and error patterns
4. **Cleanup:** Implement job to remove old profile images when updated
5. **CORS:** Configure proper CORS headers for cross-origin requests

## Migration Script

```javascript
// Add profileImageUrl field to existing users
db.users.updateMany(
  { profileImageUrl: { $exists: false } },
  { $set: { profileImageUrl: null } }
);
```
